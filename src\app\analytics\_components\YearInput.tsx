"use client";
import React from "react";

// shadcn ui
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

const YearInput = ({
  min,
  max,
  name,
  type = "text",
  label,
  value = "",
  onChange = () => {},
  required = false,
  className,
  placeholder = "",
}: {
  min?: number;
  max?: number;
  name?: string;
  type?: string;
  label?: string;
  value?: string | number | "";
  onChange?: <T extends React.ChangeEvent<HTMLInputElement>>(event: T) => void;
  required?: boolean;
  className?: string;
  placeholder?: string;
}) => (
  <div className={`grid max-w-sm items-center gap-2 ${className}`}>
    {label && (
      <Label htmlFor={label}>
        {label} {required && <span className="text-red-500">*</span>}
      </Label>
    )}
    <Input
      type={type}
      placeholder={placeholder}
      value={value}
      {...(name && { name })}
      {...(min && { min })}
      {...(max && { max })}
      onChange={onChange}
    />
  </div>
);

export default YearInput;
