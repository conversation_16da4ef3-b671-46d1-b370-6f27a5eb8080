// 各種功能的數據類型定義和模擬數據

// 姓氏分布數據
export interface SurnameData {
	id: string;
	lat: number;
	lng: number;
	landNumber: string; // 地號
	surname: string; // 姓氏
	year: string | number; // 年份
	count: number; // 該姓氏在此地號的數量
}

// 土地坐落數據
export interface LandLocationData {
	id: string;
	lat: number;
	lng: number;
	landNumber: string; // 地號
	section: string; // 地段
	area: number; // 面積
	year: number;
}

// 土地交易數據（已有 HeatmapDataPoint，這裡擴展）
export interface TransactionData {
	id: string;
	lat: number;
	lng: number;
	landNumber: string;
	transactionCount: number;
	totalValue: number;
	year: number;
	intensity: number; // 熱力圖強度
}

// 搜索半徑數據
export interface SearchRadiusData {
	centerLat: number;
	centerLng: number;
	radius: number; // 米
	lands: LandLocationData[];
}

// 地目分布數據
export interface LandTypeData {
	id: string;
	lat: number;
	lng: number;
	landNumber: string;
	landType: string; // 地目類型
	area: number;
	rent: number;
	year: number;
	color: string; // 顯示顏色
	shape: 'circle' | 'square' | 'triangle' | 'star' | 'diamond'; // 顯示形狀
}

// 地目類型配置
export const landTypeConfig = {
	田: { color: '#22c55e', shape: 'circle' as const, name: '田' },
	旱: { color: '#eab308', shape: 'square' as const, name: '旱' },
	建: { color: '#ef4444', shape: 'triangle' as const, name: '建' },
	道: { color: '#6b7280', shape: 'diamond' as const, name: '道' },
	溝: { color: '#3b82f6', shape: 'star' as const, name: '溝' },
	墓: { color: '#8b5cf6', shape: 'circle' as const, name: '墓' },
	林: { color: '#059669', shape: 'square' as const, name: '林' },
	池: { color: '#0ea5e9', shape: 'triangle' as const, name: '池' },
};

// 模擬姓氏分布數據
export function generateMockSurnameData(startYear: number, endYear: number, count: number = 50): SurnameData[] {
	const surnames = ['陳', '林', '黃', '張', '李', '王', '吳', '劉', '蔡', '楊', '許', '鄭', '謝', '郭', '洪'];
	const data: SurnameData[] = [];
	const centerLat = 22.5;
	const centerLng = 120.6;

	for (let i = 0; i < count; i++) {
		const lat = centerLat + (Math.random() - 0.5) * 0.5;
		const lng = centerLng + (Math.random() - 0.5) * 0.5;
		const year = Math.floor(Math.random() * (endYear - startYear + 1)) + startYear;
		const surname = surnames[Math.floor(Math.random() * surnames.length)];

		data.push({
			id: `surname_${i}`,
			lat,
			lng,
			landNumber: `${Math.floor(Math.random() * 1000)}-${Math.floor(Math.random() * 100)}`,
			surname,
			year,
			count: Math.floor(Math.random() * 5) + 1,
		});
	}

	return data;
}

// 模擬土地坐落數據
export function generateMockLandLocationData(count: number = 100): LandLocationData[] {
	const sections = ['中正段', '民權段', '民生段', '忠孝段', '仁愛段', '信義段', '和平段'];
	const data: LandLocationData[] = [];
	const centerLat = 22.5;
	const centerLng = 120.6;

	for (let i = 0; i < count; i++) {
		const lat = centerLat + (Math.random() - 0.5) * 0.5;
		const lng = centerLng + (Math.random() - 0.5) * 0.5;
		const section = sections[Math.floor(Math.random() * sections.length)];

		data.push({
			id: `land_${i}`,
			lat,
			lng,
			landNumber: `${Math.floor(Math.random() * 1000)}-${Math.floor(Math.random() * 100)}`,
			section,
			area: Math.floor(Math.random() * 1000) + 100,
			year: 2024,
		});
	}

	return data;
}

// 模擬交易數據
export function generateMockTransactionData(startYear: number, endYear: number, count: number = 80): TransactionData[] {
	const data: TransactionData[] = [];
	const centerLat = 22.5;
	const centerLng = 120.6;

	for (let i = 0; i < count; i++) {
		const lat = centerLat + (Math.random() - 0.5) * 0.5;
		const lng = centerLng + (Math.random() - 0.5) * 0.5;
		const year = Math.floor(Math.random() * (endYear - startYear + 1)) + startYear;

		data.push({
			id: `transaction_${i}`,
			lat,
			lng,
			landNumber: `${Math.floor(Math.random() * 1000)}-${Math.floor(Math.random() * 100)}`,
			transactionCount: Math.floor(Math.random() * 20) + 1,
			totalValue: Math.floor(Math.random() * 10000000) + 1000000,
			year,
			intensity: 0,
		});
	}

	return data;
}

// 模擬地目分布數據
export function generateMockLandTypeData(startYear: number, endYear: number, count: number = 60): LandTypeData[] {
	const landTypes = Object.keys(landTypeConfig);
	const data: LandTypeData[] = [];
	const centerLat = 22.5;
	const centerLng = 120.6;

	for (let i = 0; i < count; i++) {
		const lat = centerLat + (Math.random() - 0.5) * 0.5;
		const lng = centerLng + (Math.random() - 0.5) * 0.5;
		const year = Math.floor(Math.random() * (endYear - startYear + 1)) + startYear;
		const landType = landTypes[Math.floor(Math.random() * landTypes.length)];
		const config = landTypeConfig[landType as keyof typeof landTypeConfig];

		data.push({
			id: `landtype_${i}`,
			lat,
			lng,
			landNumber: `${Math.floor(Math.random() * 1000)}-${Math.floor(Math.random() * 100)}`,
			landType,
			area: Math.floor(Math.random() * 1000) + 100,
			rent: Math.floor(Math.random() * 10000) + 1000,
			year,
			color: config.color,
			shape: config.shape,
		});
	}

	return data;
}

// 生成搜索半徑數據
export function generateSearchRadiusData(
  centerLat: number,
  centerLng: number,
  radius: number
): SearchRadiusData {
  const lands = generateMockLandLocationData(30).filter(land => {
    // 簡單的距離計算（實際應該使用更精確的地理距離計算）
    const distance = Math.sqrt(
      Math.pow((land.lat - centerLat) * 111000, 2) + 
      Math.pow((land.lng - centerLng) * 111000 * Math.cos(centerLat * Math.PI / 180), 2)
    );
    return distance <= radius;
  });

  return {
    centerLat,
    centerLng,
    radius,
    lands
  };
}

// 根據時間範圍過濾數據的通用函數
export function filterDataByTimeRange<T extends { year: number }>(
  data: T[],
  startYear: number,
  endYear: number
): T[] {
  return data.filter(item => item.year >= startYear && item.year <= endYear);
}
