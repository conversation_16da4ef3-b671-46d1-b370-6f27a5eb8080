import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import { LandTypeData } from './feature-data';

export function cn(...inputs: ClassValue[]) {
	return twMerge(clsx(inputs));
}

// XXX => X
export const firstChar = (s: string) => Array.from(s.trim())[0] ?? '';

// yyyy-mm-dd => yyyy
export const keepYearStrict = (s: string) => s.match(/(\d{4})(?=-\d{2}-\d{2})/)?.[1] ?? '';

export function pickFirstInRangeById(data: LandTypeData[], currentYear: number, endYear: number): LandTypeData[] {
	const seen = new Set<string>();
	const out: LandTypeData[] = [];

	// 先依年份升冪，確保同 id 只會拿到最早的一筆
	const sorted = data.slice().sort((a, b) => Number(a.year) - Number(b.year));

	for (const item of sorted) {
		const y = Number(item.year);
		if (Number.isNaN(y)) continue; // 忽略壞資料
		if (y < currentYear || y > endYear) continue;
		if (seen.has(item.id)) continue;

		seen.add(item.id);
		out.push(item);
	}
	return out;
}