'use client'

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { 
  Flame, 
  Settings, 
  RefreshCw,
  X,
  TrendingUp,
  Activity
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface HeatmapPanelProps {
  isVisible: boolean;
  onVisibilityChange: (visible: boolean) => void;
  onOptionsChange: (options: any) => void;
  onRefreshData: () => void;
  dataCount: number;
  className?: string;
  onClose: () => void;
}

export function HeatmapPanel({
  isVisible,
  onVisibilityChange,
  onOptionsChange,
  onRefreshData,
  dataCount,
  className,
  onClose
}: HeatmapPanelProps) {
  const [radius, setRadius] = useState([25]);
  const [blur, setBlur] = useState([15]);
  const [maxZoom, setMaxZoom] = useState([17]);
  const [minOpacity, setMinOpacity] = useState([0.4]);
  const [gradientType, setGradientType] = useState('default');

  // 預設的漸層配色方案
  const gradientPresets = {
    default: {
      name: '經典',
      colors: {
        0.0: '#3388ff',
        0.2: '#00ff88', 
        0.4: '#ffff00',
        0.6: '#ff8800',
        0.8: '#ff4400',
        1.0: '#ff0000'
      }
    },
    cool: {
      name: '冷色調',
      colors: {
        0.0: '#00ffff',
        0.3: '#0080ff',
        0.6: '#8000ff',
        1.0: '#ff00ff'
      }
    },
    warm: {
      name: '暖色調',
      colors: {
        0.0: '#ffff00',
        0.4: '#ff8000',
        0.7: '#ff4000',
        1.0: '#ff0000'
      }
    },
    monochrome: {
      name: '單色',
      colors: {
        0.0: '#ffffff',
        0.5: '#888888',
        1.0: '#000000'
      }
    }
  };

  // 當設定改變時通知父組件
  const handleOptionsChange = () => {
    const options = {
      radius: radius[0],
      blur: blur[0],
      maxZoom: maxZoom[0],
      minOpacity: minOpacity[0] / 100,
      gradient: gradientPresets[gradientType as keyof typeof gradientPresets].colors
    };
    onOptionsChange(options);
  };

  // 監聽設定變化
  const handleRadiusChange = (value: number[]) => {
    setRadius(value);
    setTimeout(handleOptionsChange, 100);
  };

  const handleBlurChange = (value: number[]) => {
    setBlur(value);
    setTimeout(handleOptionsChange, 100);
  };

  const handleMaxZoomChange = (value: number[]) => {
    setMaxZoom(value);
    setTimeout(handleOptionsChange, 100);
  };

  const handleMinOpacityChange = (value: number[]) => {
    setMinOpacity(value);
    setTimeout(handleOptionsChange, 100);
  };

  const handleGradientChange = (type: string) => {
    setGradientType(type);
    setTimeout(handleOptionsChange, 100);
  };

  return (
    <Card className={cn("w-80", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base flex items-center">
            <Flame className="h-4 w-4 mr-2 text-orange-500" />
            熱力圖設定
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
            onClick={onClose}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* 基本控制 */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Activity className="h-4 w-4 text-green-500" />
              <span className="text-sm font-medium">顯示熱力圖</span>
            </div>
            <Switch
              checked={isVisible}
              onCheckedChange={onVisibilityChange}
            />
          </div>

          <div className="flex items-center justify-between p-2 bg-muted/50 rounded-md">
            <span className="text-sm text-muted-foreground">數據點數量</span>
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-3 w-3 text-blue-500" />
              <span className="font-medium">{dataCount}</span>
            </div>
          </div>

          <Button
            variant="outline"
            size="sm"
            className="w-full"
            onClick={onRefreshData}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            重新載入數據
          </Button>
        </div>

        <Separator />

        {/* 進階設定 */}
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <Settings className="h-4 w-4 text-gray-500" />
            <span className="text-sm font-medium">進階設定</span>
          </div>

          {/* 半徑設定 */}
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-xs font-medium">半徑</span>
              <span className="text-xs text-muted-foreground">{radius[0]}px</span>
            </div>
            <Slider
              value={radius}
              onValueChange={handleRadiusChange}
              max={50}
              min={10}
              step={1}
              className="w-full"
            />
          </div>

          {/* 模糊設定 */}
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-xs font-medium">模糊程度</span>
              <span className="text-xs text-muted-foreground">{blur[0]}px</span>
            </div>
            <Slider
              value={blur}
              onValueChange={handleBlurChange}
              max={30}
              min={5}
              step={1}
              className="w-full"
            />
          </div>

          {/* 最小透明度 */}
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-xs font-medium">最小透明度</span>
              <span className="text-xs text-muted-foreground">{minOpacity[0]}%</span>
            </div>
            <Slider
              value={minOpacity}
              onValueChange={handleMinOpacityChange}
              max={100}
              min={10}
              step={5}
              className="w-full"
            />
          </div>

          {/* 最大縮放級別 */}
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-xs font-medium">最大縮放級別</span>
              <span className="text-xs text-muted-foreground">{maxZoom[0]}</span>
            </div>
            <Slider
              value={maxZoom}
              onValueChange={handleMaxZoomChange}
              max={20}
              min={10}
              step={1}
              className="w-full"
            />
          </div>
        </div>

        <Separator />

        {/* 配色方案 */}
        <div className="space-y-3">
          <span className="text-sm font-medium">配色方案</span>
          <div className="grid grid-cols-2 gap-2">
            {Object.entries(gradientPresets).map(([key, preset]) => (
              <Button
                key={key}
                variant={gradientType === key ? "default" : "outline"}
                size="sm"
                className="text-xs"
                onClick={() => handleGradientChange(key)}
              >
                {preset.name}
              </Button>
            ))}
          </div>
          
          {/* 配色預覽 */}
          <div className="h-4 rounded-md overflow-hidden" 
               style={{
                 background: `linear-gradient(to right, ${Object.values(
                   gradientPresets[gradientType as keyof typeof gradientPresets].colors
                 ).join(', ')})`
               }}>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
