/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import { useLayoutEffect } from "react";

// components
import TimeLine from "./TimeLine";
import AnalyticChart from "./AnalyticChart";

const LandRightAnalytic = ({
  analyticItem,
}: {
  analyticItem: Record<string, any>;
}): React.JSX.Element => {
  // const [isLoading, setIsLoading] = useState<boolean>(true);

  const { title } = analyticItem;

  console.log("???", analyticItem);

  // get data
  useLayoutEffect(() => {}, []);

  return (
    <div className="grid grid-row-3 gap-4">
      <div className="row-span-1 p-2 text-2xl font-semibold">{title}</div>
      <div className="row-span-1">
        <TimeLine />
      </div>
      <div className="row-span-1">
        <AnalyticChart data={[{ id: "123" }]} />
      </div>
    </div>
  );
};

export default LandRightAnalytic;
