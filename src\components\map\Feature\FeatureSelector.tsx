'use client'

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Users,
  MapPin,
  Flame,
  Search,
  Shapes,
  X
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { mapTips } from '@/config/map.config';

export type FeatureType = 
  | 'surname-distribution'    // 姓氏分布
  | 'land-location'          // 土地坐落
  | 'transaction-heatmap'    // 土地交易次數
  | 'search-radius'          // 搜索半徑
  | 'land-type-distribution' // 各種地目分布
  | null;

interface FeatureSelectorProps {
  selectedFeature: FeatureType;
  onFeatureSelect: (feature: FeatureType) => void;
  onClose: () => void;
  className?: string;
}

const features = [
  {
    id: 'surname-distribution' as const,
    name: '姓氏分布',
    description: '結合時間軸，每個地號上顯示姓氏',
    icon: Users,
    color: 'text-blue-600',
    bgColor: 'bg-blue-50 hover:bg-blue-100'
  },
  {
    id: 'land-location' as const,
    name: '土地坐落',
    description: '顯示地號，拉遠時呈現地段cluster，拉近時呈現地號',
    icon: MapPin,
    color: 'text-green-600',
    bgColor: 'bg-green-50 hover:bg-green-100'
  },
  {
    id: 'transaction-heatmap' as const,
    name: '土地交易次數',
    description: '結合時間軸，熱區圖呈現，交易次數愈多，顏色愈深',
    icon: Flame,
    color: 'text-red-600',
    bgColor: 'bg-red-50 hover:bg-red-100'
  },
  {
    id: 'search-radius' as const,
    name: '搜索半徑',
    description: '地圖上呈現方圓內的所有地號，下方列表顯示地號資訊',
    icon: Search,
    color: 'text-purple-600',
    bgColor: 'bg-purple-50 hover:bg-purple-100'
  },
  {
    id: 'land-type-distribution' as const,
    name: '各種地目分布',
    description: '結合時間軸，不同地目使用不同顏色和圖形表示',
    icon: Shapes,
    color: 'text-orange-600',
    bgColor: 'bg-orange-50 hover:bg-orange-100'
  }
];

export function FeatureSelector({
  selectedFeature,
  onFeatureSelect,
  onClose,
  className
}: FeatureSelectorProps) {
  const selectedFeatureConfig = selectedFeature ? features.find(f => f.id === selectedFeature) : null;

  return (
    <Card className={cn("w-80", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base">功能選擇</CardTitle>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
            onClick={onClose}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        <div className="space-y-2">
          <label className="text-sm font-medium">選擇功能類型</label>
          <Select
            value={selectedFeature || undefined}
            onValueChange={(value) => {
              if (value) {
                onFeatureSelect(value as FeatureType);
              }
            }}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="請選擇要顯示的功能..." />
            </SelectTrigger>
            <SelectContent position="popper" className="z-[1000]">
              {features.map((feature) => {
                const Icon = feature.icon;
                return (
                  <SelectItem key={feature.id} value={feature.id}>
                    <div className="flex items-center space-x-2">
                      <Icon className={cn("h-4 w-4", feature.color)} />
                      <span>{feature.name}</span>
                    </div>
                  </SelectItem>
                );
              })}
            </SelectContent>
          </Select>
        </div>

        {selectedFeatureConfig && (
          <div className={cn("p-3 rounded-md", selectedFeatureConfig.bgColor)}>
            <div className="flex items-start space-x-3">
              <selectedFeatureConfig.icon className={cn("h-5 w-5 mt-0.5", selectedFeatureConfig.color)} />
              <div className="flex-1 space-y-1">
                <div className="font-medium">{selectedFeatureConfig.name}</div>
                <div className="text-xs text-muted-foreground">
                  {selectedFeatureConfig.description}
                </div>
              </div>
            </div>
          </div>
        )}

        {selectedFeature && (
          <div className="text-xs text-muted-foreground bg-muted/50 p-3 rounded-md">
            <strong>提示：</strong>
            {mapTips[selectedFeature]}
          </div>
        )}

        <div className="flex space-x-2">
          <Button
            variant="outline"
            className="flex-1"
            onClick={() => onFeatureSelect(null)}
            disabled={!selectedFeature}
          >
            清除選擇
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
