'use client'

import { useEffect } from 'react';
import { useMap } from 'react-leaflet';
import { SurnameData } from '@/lib/feature-data';

interface SurnameLayerProps {
  data: SurnameData[];
  visible?: boolean;
}

export function SurnameLayer({ data, visible = true }: SurnameLayerProps) {
  const map = useMap();

  useEffect(() => {
    if (!map) return;

	let surnameLayerGroup: any = null;
	let isMounted = true;

	// 動態導入 Leaflet 避免 SSR 問題
	const loadLeaflet = async () => {
		const L = (await import('leaflet')).default;

		// 檢查組件是否仍然掛載
		if (!isMounted) return;

		// 創建圖層組
		surnameLayerGroup = L.layerGroup();

		// 只有當可見且有數據時才創建標記
		if (visible && data.length > 0) {
			// 為每個數據點創建標記
			data.forEach((item) => {
				// 創建自定義圖標
				const divIcon = L.divIcon({
					html: `
            <div style="
              background: rgba(59, 130, 246, 0.8);
              color: white;
              padding: 2px 6px;
              border-radius: 4px;
              font-size: 12px;
              font-weight: bold;
              text-align: center;
              border: 2px solid white;
              box-shadow: 0 2px 4px rgba(0,0,0,0.2);
              min-width: 24px;
            ">
              ${item.surname}
            </div>
          `,
					className: 'surname-marker',
					iconSize: [30, 20],
					iconAnchor: [15, 10],
				});

				// 創建標記
				const marker = L.marker([item.lat, item.lng], { icon: divIcon });

				// 添加彈出窗口
				marker.bindPopup(`
          <div style="font-size: 14px;">
            <strong>姓氏:</strong> ${item.surname}<br/>
            <strong>地號:</strong> ${item.landNumber}<br/>
            <strong>年份:</strong> ${item.year}<br/>
          </div>
        `);

				// 添加到圖層組
				surnameLayerGroup.addLayer(marker);
			});
		}

		// 只有當可見時才添加到地圖
		if (visible && isMounted) {
			surnameLayerGroup.addTo(map);
		}
	};

	loadLeaflet();

	// 清理函數
	return () => {
		isMounted = false;
		if (surnameLayerGroup && map) {
			try {
				map.removeLayer(surnameLayerGroup);
			} catch (error) {
				// 忽略移除圖層時的錯誤
				console.warn('Error removing surname layer:', error);
			}
		}
	};
  }, [map, data, visible]);

  return null;
}
