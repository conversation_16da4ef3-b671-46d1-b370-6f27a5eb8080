'use client'

import { useEffect, useState, useMemo } from 'react';
import { useMap } from 'react-leaflet';
import { SurnameData } from '@/lib/feature-data';
import { filterDataByBounds, sampleDataByZoom, debounce, type MapBounds } from '@/lib/map-performance';

interface SurnameLayerProps {
	data: SurnameData[];
	visible?: boolean;
}

// 創建聚合圖標
function createSurnameClusterIcon(surname: string, count: number): string {
	return `
    <div style="
      background: rgba(59, 130, 246, 0.9);
      color: white;
      padding: 8px;
      border-radius: 50%;
      font-size: 12px;
      font-weight: bold;
      text-align: center;
      border: 3px solid white;
      box-shadow: 0 2px 8px rgba(0,0,0,0.3);
      min-width: 35px;
      min-height: 35px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      line-height: 1;
    ">
      <div style="font-size: 8px; margin-bottom: 1px;">${surname}</div>
      <div>${count}</div>
    </div>
  `;
}

export function SurnameLayer({ data, visible = true }: SurnameLayerProps) {
	const map = useMap();
	const [currentZoom, setCurrentZoom] = useState(10);
	const [mapBounds, setMapBounds] = useState<MapBounds | null>(null);

	// 使用 useMemo 優化數據處理
	const optimizedData = useMemo(() => {
		if (!data.length || !mapBounds) return data;

		// 1. 先根據視窗範圍過濾
		const boundsFiltered = filterDataByBounds(data, mapBounds, 0.2);

		// 2. 根據縮放層級進行採樣
		const maxPoints = currentZoom <= 11 ? 50 : currentZoom <= 14 ? 500 : 1000;
		const sampled = sampleDataByZoom(boundsFiltered, currentZoom, maxPoints);

		return sampled;
	}, [data, mapBounds, currentZoom]);

	// 防抖的邊界更新函數
	const debouncedUpdateBounds = useMemo(
		() =>
			debounce((bounds: MapBounds) => {
				setMapBounds(bounds);
			}, 300),
		[]
	);

	// 監聽縮放和移動變化
	useEffect(() => {
		if (!map) return;

		// 設置初始值
		setCurrentZoom(map.getZoom());
		const bounds = map.getBounds();
		setMapBounds({
			north: bounds.getNorth(),
			south: bounds.getSouth(),
			east: bounds.getEast(),
			west: bounds.getWest(),
		});

		// 監聽縮放變化
		const handleZoomEnd = () => {
			const newZoom = map.getZoom();
			setCurrentZoom(newZoom);

			const bounds = map.getBounds();
			debouncedUpdateBounds({
				north: bounds.getNorth(),
				south: bounds.getSouth(),
				east: bounds.getEast(),
				west: bounds.getWest(),
			});
		};

		// 監聽移動變化
		const handleMoveEnd = () => {
			const bounds = map.getBounds();
			debouncedUpdateBounds({
				north: bounds.getNorth(),
				south: bounds.getSouth(),
				east: bounds.getEast(),
				west: bounds.getWest(),
			});
		};

		map.on('zoomend', handleZoomEnd);
		map.on('moveend', handleMoveEnd);

		return () => {
			map.off('zoomend', handleZoomEnd);
			map.off('moveend', handleMoveEnd);
		};
	}, [map, debouncedUpdateBounds]);

	useEffect(() => {
		if (!map) return;

		let surnameLayerGroup: any = null;
		let isMounted = true;

		// 動態導入 Leaflet 避免 SSR 問題
		const loadLeaflet = async () => {
			const L = (await import('leaflet')).default;

			// 檢查組件是否仍然掛載
			if (!isMounted) return;

			// 創建圖層組
			surnameLayerGroup = L.layerGroup();

			// 只有當可見且有數據時才創建標記
			if (visible && optimizedData.length > 0) {
				// 性能監控：只在開發環境下輸出
				if (process.env.NODE_ENV === 'development') {
					console.warn(`SurnameLayer: 渲染 ${optimizedData.length}/${data.length} 個標記 (縮放: ${currentZoom})`);
				}

				// 根據縮放層級決定顯示方式
				if (currentZoom <= 11) {
					// 拉遠時顯示姓氏聚合
					const surnameGroups: { [key: string]: SurnameData[] } = {};

					// 按姓氏分組
					optimizedData.forEach((item) => {
						if (!surnameGroups[item.surname]) {
							surnameGroups[item.surname] = [];
						}
						surnameGroups[item.surname].push(item);
					});

					// 為每個姓氏創建聚合標記
					Object.entries(surnameGroups).forEach(([surname, items]) => {
						if (items.length === 0) return;

						// 計算姓氏中心點
						const centerLat = items.reduce((sum, item) => sum + item.lat, 0) / items.length;
						const centerLng = items.reduce((sum, item) => sum + item.lng, 0) / items.length;

						// 創建聚合圖標
						const clusterIcon = L.divIcon({
							html: createSurnameClusterIcon(surname, items.length),
							className: 'surname-cluster-marker',
							iconSize: [40, 40],
							iconAnchor: [20, 20],
						});

						const marker = L.marker([centerLat, centerLng], { icon: clusterIcon });

						marker.bindPopup(`
              <div style="font-size: 14px;">
                <strong>${surname} 姓氏聚合</strong><br/>
                <strong>數量:</strong> ${items.length} 筆<br/>
                <strong>年份範圍:</strong> ${Math.min(...items.map((i) => i.year))} - ${Math.max(...items.map((i) => i.year))}<br/>
                <em>放大地圖查看個別地號</em>
              </div>
            `);

						surnameLayerGroup.addLayer(marker);
					});
				} else {
					// 拉近時顯示個別標記
					optimizedData.forEach((item) => {
						// 創建自定義圖標
						const divIcon = L.divIcon({
							html: `
              <div style="
                background: rgba(59, 130, 246, 0.8);
                color: white;
                padding: 2px 6px;
                border-radius: 4px;
                font-size: ${currentZoom > 14 ? '12px' : '10px'};
                font-weight: bold;
                text-align: center;
                border: 2px solid white;
                box-shadow: 0 2px 4px rgba(0,0,0,0.2);
                min-width: 24px;
                white-space: nowrap;
              ">
                ${item.surname}
              </div>
            `,
							className: 'surname-marker',
							iconSize: [30, 20],
							iconAnchor: [15, 10],
						});

						// 創建標記
						const marker = L.marker([item.lat, item.lng], { icon: divIcon });

						// 添加彈出窗口
						marker.bindPopup(`
            <div style="font-size: 14px;">
              <strong>姓氏:</strong> ${item.surname}<br/>
              <strong>地號:</strong> ${item.landNumber}<br/>
              <strong>年份:</strong> ${item.year}<br/>
            </div>
          `);

						// 添加到圖層組
						surnameLayerGroup.addLayer(marker);
					});
				}
			}

			// 只有當可見時才添加到地圖
			if (visible && isMounted) {
				surnameLayerGroup.addTo(map);
			}
		};

		loadLeaflet();

		// 清理函數
		return () => {
			isMounted = false;
			if (surnameLayerGroup && map) {
				try {
					map.removeLayer(surnameLayerGroup);
				} catch (error) {
					// 忽略移除圖層時的錯誤
					console.warn('Error removing surname layer:', error);
				}
			}
		};
	}, [map, optimizedData, visible, currentZoom]);

	return null;
}
