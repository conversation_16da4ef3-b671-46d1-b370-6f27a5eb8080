Stack trace:
Frame         Function      Args
0007FFFFB6C0  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFFA5C0) msys-2.0.dll+0x1FEBA
0007FFFFB6C0  0002100467F9 (000000000000, 000000000000, 000000000000, 6FFFFFFF0BC9) msys-2.0.dll+0x67F9
0007FFFFB6C0  000210046832 (000210285FF9, 0007FFFFB578, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFB6C0  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFB6C0  0002100690B4 (0007FFFFB6D0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
000000000006  00021006A49D (0007FFFFB6D0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 zsh.exe
7FFFCABA0000 ntdll.dll
7FFFC9FB0000 KERNEL32.DLL
7FFFC7F30000 KERNELBASE.dll
000210040000 msys-2.0.dll
000539B50000 msys-zsh-5.9.dll
0005FCB10000 msys-ncursesw6.dll
7FFFC9120000 advapi32.dll
7FFFC9590000 msvcrt.dll
7FFFCA8F0000 sechost.dll
7FFFC9000000 RPCRT4.dll
7FFFC7260000 CRYPTBASE.DLL
7FFFC8820000 bcryptPrimitives.dll
