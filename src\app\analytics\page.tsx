/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import { useState, useLayoutEffect } from "react";
import { BarChart3 } from "lucide-react";

// shadcn components

// components
import AnalyticSidebar from "./_components/AnalyticSidebar";
import LandRightAnalytic from "./_components/LandRightAnalytic";
import LandIdentificationAnalytic from "./_components/LandIdentificationAnalytic";

export default function AnalyticsPage() {
  const [analyticItem, setAnalyticItem] = useState<Record<string, any>>(null!);

  // local configs
  const AllAnalyticData = [
    {
      id: "LandRight",
      icon: null,
      title: "權利變更原因及次數時序統計",
      onClick: () => {
        console.log("Hello1");
        setAnalyticItem(AllAnalyticData[0]);
      },
      groupLabel: "土地權利變更",
      sidebarMenuLabelClassName: "text-lg",
    },
    {
      id: "LandIdentification",
      icon: null,
      title: "地目變更次數時序統計",
      onClick: () => {
        console.log("Hello2");
        setAnalyticItem(AllAnalyticData[1]);
      },
      groupLabel: "土地標示變更",
      sidebarMenuLabelClassName: "text-lg",
    },
  ];

  useLayoutEffect(() => {
    setAnalyticItem(AllAnalyticData[0]);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4 flex items-center">
            <BarChart3 className="w-8 h-8 mr-3 text-blue-600" />
            統計分析
          </h1>
          <p className="text-lg text-gray-600">
            透過資料整理與統計歸納，解析土地所有權與利用的歷史圖像
          </p>
        </div>
        <div className="grid grid-cols-12 gap-4 rounded-lg shadow-lg border border-gray-200 p-4">
          <div className="col-span-3">
            <AnalyticSidebar data={AllAnalyticData} />
          </div>
          <div className="col-span-9">
            {analyticItem?.id === "LandRight" && (
              <LandRightAnalytic analyticItem={analyticItem} />
            )}
            {analyticItem?.id === "LandIdentification" && (
              <LandIdentificationAnalytic />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
