import { firstChar, keepYearStrict } from './utils';

const BASE_URL = process.env.NEXT_PUBLIC_API_URL;
const LANGUAGE = process.env.NEXT_PUBLIC_API_LANGUAGE;

export const API_ENDPOINTS = {
	GIS_DATA: `${BASE_URL}/${LANGUAGE}/gis/table/data/1.0?limit=-1&offset=0`,
	GIS_TRANSACTIONS_DATA: `${BASE_URL}/${LANGUAGE}/client/gis/transactions/data/1.0?limit=-1&offset=0`,
	GIS_LAND_LOCATION_DATA: `${BASE_URL}/${LANGUAGE}/client/gis/land-location/data/1.0?limit=-1&offset=0`,
	GIS_SURNAME_DATA: `${BASE_URL}/${LANGUAGE}/client/gis/surname/data/1.0?limit=-1&offset=0`,
	GIS_LAND_TYPE_DATA: `${BASE_URL}/${LANGUAGE}/client/gis/land-type/data/1.0?limit=-1&offset=0`,
	GIS_LAND_TRANSACTION_DATA: `${BASE_URL}/${LANGUAGE}/client/gis/land-transaction/data/1.0?limit=-1&offset=0`,
};

// GIS 數據獲取函數
export async function fetchGISData(endpoint: string) {
	try {
		const response = await fetch(endpoint);
		if (!response.ok) {
			throw new Error(`HTTP error! status: ${response.status}`);
		}
		const data = await response.json();
		return data;
	} catch (error) {
		console.error('Error fetching GIS data:', error);
		throw error;
	}
}

// 根據功能類型獲取對應的 API 端點
export function getGISEndpoint(featureType: string): string {
	switch (featureType) {
		case 'surname-distribution':
			return API_ENDPOINTS.GIS_SURNAME_DATA;
		case 'land-location':
			return API_ENDPOINTS.GIS_LAND_LOCATION_DATA;
		case 'land-type-distribution':
			return API_ENDPOINTS.GIS_LAND_TYPE_DATA;
		case 'transaction-heatmap':
			return API_ENDPOINTS.GIS_TRANSACTIONS_DATA;
		case 'land-transaction':
			return API_ENDPOINTS.GIS_LAND_TRANSACTION_DATA;
		default:
			throw new Error(`Unknown feature type: ${featureType}`);
	}
}

// 數據轉換函數 - 將 API 數據轉換為前端需要的格式
export function transformAPIData(apiData: any, featureType: string): any[] {
	if (!apiData || !Array.isArray(apiData)) {
		console.warn('Invalid API data format:', apiData);
		return [];
	}

	try {
		switch (featureType) {
			case 'surname-distribution':
				return apiData.map((item: any, index: number) => ({
					id: item.id || `surname-${index}`,
					lat: parseFloat(item.lat || item.latitude || 0),
					lng: parseFloat(item.long || item.longitude || 0),
					landNumber: `${item.landName}${item.landSerialNumber}` || item.land_number || '',
					surname: firstChar(item.ownerName) || '',
					year: keepYearStrict(item.time) || '',
					// count: parseInt(item.count || 1),
				}));

			case 'land-location':
				return apiData.map((item: any, index: number) => ({
					id: item.id || `land-${index}`,
					lat: parseFloat(item.lat || item.latitude || 0),
					lng: parseFloat(item.long || item.longitude || 0),
					landNumber: item.landSerialNumber || item.land_number || '',
					section: item.landName || item.land_name || '',
					area: parseFloat(item.area || item.size || 0),
					year: parseInt(item.year || new Date().getFullYear()),
				}));

			case 'land-type-distribution':
				return apiData.map((item: any, index: number) => ({
					id: item.id || `landtype-${index}`,
					lat: parseFloat(item.lat || item.latitude || 0),
					lng: parseFloat(item.long || item.longitude || 0),
					landNumber: `${item.landName}${item.landSerialNumber}` || item.land_number || '',
					landType: item.category || item.land_type || item.type || '田',
					area: parseFloat(item.area || item.size || 0),
					rent: parseFloat(item.rent || item.rental || 0),
					year: keepYearStrict(item.time),
					// color: item.color || getDefaultLandTypeColor(item.landType || item.land_type || item.type),
					// shape: item.shape || getDefaultLandTypeShape(item.landType || item.land_type || item.type),
				}));

			case 'transaction-heatmap':
				return apiData.map((item: any, index: number) => ({
					id: item.id || `transaction-${index}`,
					lat: parseFloat(item.lat || item.latitude || 0),
					lng: parseFloat(item.long || item.longitude || 0),
					landNumber: item.landNumber || item.land_number || '',
					transactionCount: parseInt(item.transactionCount || item.transaction_count || 1),
					totalValue: parseFloat(item.totalValue || item.total_value || 0),
					year: parseInt(item.year || new Date().getFullYear()),
					intensity: parseFloat(item.intensity || Math.min(1, (item.transactionCount || 1) / 10)),
				}));

			default:
				return apiData;
		}
	} catch (error) {
		console.error('Error transforming API data:', error);
		return [];
	}
}

// 獲取地目類型的默認顏色
function getDefaultLandTypeColor(landType: string): string {
	const colorMap: { [key: string]: string } = {
		田: '#22c55e',
		旱: '#eab308',
		建: '#ef4444',
		道: '#6b7280',
		溝: '#3b82f6',
		墓: '#8b5cf6',
		林: '#059669',
		池: '#0ea5e9',
	};
	return colorMap[landType] || '#6b7280';
}

// 獲取地目類型的默認形狀
function getDefaultLandTypeShape(landType: string): 'circle' | 'square' | 'triangle' | 'star' | 'diamond' {
	const shapeMap: { [key: string]: 'circle' | 'square' | 'triangle' | 'star' | 'diamond' } = {
		田: 'circle',
		旱: 'square',
		建: 'triangle',
		道: 'diamond',
		溝: 'star',
		墓: 'circle',
		林: 'square',
		池: 'triangle',
	};
	return shapeMap[landType] || 'circle';
}
