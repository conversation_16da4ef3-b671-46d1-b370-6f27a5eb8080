import { Building2, TrendingUp, TrendingDown, Bar<PERSON>hart3, <PERSON><PERSON><PERSON> } from "lucide-react";

export default function HSNAPage() {
  const supplyData = [
    { region: "台北市", supply: 12500, demand: 15000, ratio: 0.83 },
    { region: "新北市", supply: 18000, demand: 16500, ratio: 1.09 },
    { region: "桃園市", supply: 8500, demand: 9200, ratio: 0.92 },
    { region: "台中市", supply: 11000, demand: 10800, ratio: 1.02 }
  ];

  const trends = [
    { month: "2023-10", supply: 45000, demand: 48000 },
    { month: "2023-11", supply: 46500, demand: 47500 },
    { month: "2023-12", supply: 48000, demand: 49000 },
    { month: "2024-01", supply: 50000, demand: 51500 }
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4 flex items-center">
            <Building2 className="w-8 h-8 mr-3 text-blue-600" />
            HSNA 住宅供需分析
          </h1>
          <p className="text-lg text-gray-600">
            Housing Supply and Needs Assessment - 提供住宅市場供需分析與預測
          </p>
        </div>

        {/* Key Metrics */}
        {/* <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">總供給量</p>
                <p className="text-2xl font-bold text-blue-600">50,000</p>
                <p className="text-xs text-gray-500">戶</p>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <Building2 className="w-6 h-6 text-blue-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center">
              <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
              <span className="text-sm text-green-600">+8.5% 較上月</span>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">總需求量</p>
                <p className="text-2xl font-bold text-green-600">51,500</p>
                <p className="text-xs text-gray-500">戶</p>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <TrendingUp className="w-6 h-6 text-green-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center">
              <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
              <span className="text-sm text-green-600">+5.1% 較上月</span>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">供需比</p>
                <p className="text-2xl font-bold text-orange-600">0.97</p>
                <p className="text-xs text-gray-500">供給/需求</p>
              </div>
              <div className="p-3 bg-orange-100 rounded-full">
                <BarChart3 className="w-6 h-6 text-orange-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center">
              <TrendingDown className="w-4 h-4 text-red-500 mr-1" />
              <span className="text-sm text-red-600">-2.1% 較上月</span>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">缺口</p>
                <p className="text-2xl font-bold text-red-600">1,500</p>
                <p className="text-xs text-gray-500">戶</p>
              </div>
              <div className="p-3 bg-red-100 rounded-full">
                <TrendingDown className="w-6 h-6 text-red-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center">
              <TrendingUp className="w-4 h-4 text-red-500 mr-1" />
              <span className="text-sm text-red-600">需求缺口擴大</span>
            </div>
          </div>
        </div> */}

        {/* Regional Analysis */}
        {/* <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <div className="bg-white rounded-lg shadow-lg border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900 flex items-center">
                <PieChart className="w-5 h-5 mr-2 text-blue-600" />
                各地區供需分析
              </h2>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {supplyData.map((item, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex justify-between items-center mb-2">
                      <h3 className="font-medium text-gray-900">{item.region}</h3>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        item.ratio >= 1 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {item.ratio >= 1 ? '供過於求' : '供不應求'}
                      </span>
                    </div>
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <p className="text-gray-600">供給</p>
                        <p className="font-semibold text-blue-600">{item.supply.toLocaleString()}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">需求</p>
                        <p className="font-semibold text-green-600">{item.demand.toLocaleString()}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">比率</p>
                        <p className="font-semibold text-gray-900">{item.ratio.toFixed(2)}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-lg border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900 flex items-center">
                <BarChart3 className="w-5 h-5 mr-2 text-green-600" />
                趨勢分析
              </h2>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {trends.map((item, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="text-sm font-medium text-gray-900">
                      {item.month}
                    </div>
                    <div className="flex space-x-4 text-sm">
                      <div className="text-blue-600">
                        供給: {item.supply.toLocaleString()}
                      </div>
                      <div className="text-green-600">
                        需求: {item.demand.toLocaleString()}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                <h3 className="font-medium text-blue-900 mb-2">預測分析</h3>
                <p className="text-sm text-blue-800">
                  根據歷史數據分析，預計未來 3 個月住宅需求將持續增長 3-5%，
                  建議加強供給端政策調控。
                </p>
              </div>
            </div>
          </div>
        </div> */}

        {/* Action Items */}
        {/* <div className="bg-white rounded-lg shadow-lg border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">政策建議</h2>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <h3 className="font-medium text-gray-900">短期措施</h3>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    加速現有建案審批流程
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    釋出更多可建築用地
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    推動社會住宅興建計畫
                  </li>
                </ul>
              </div>
              
              <div className="space-y-3">
                <h3 className="font-medium text-gray-900">長期規劃</h3>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    建立住宅供需預警機制
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    完善都市計畫與住宅政策
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    強化跨區域協調機制
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div> */}
      </div>
    </div>
  );
}
