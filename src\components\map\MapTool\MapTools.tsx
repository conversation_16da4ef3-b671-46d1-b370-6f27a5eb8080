'use client'

import { useState, useEffect } from 'react';
import { Ruler, Square, MapPin, Download, RotateCcw } from 'lucide-react';

// 動態導入 Leaflet 以避免 SSR 問題
let L: any = null;
if (typeof window !== 'undefined') {
  L = require('leaflet');
}

interface MapToolsProps {
  map: any | null;
}

export function MapTools({ map }: MapToolsProps) {
  const [activeTool, setActiveTool] = useState<string | null>(null);
  const [measurements, setMeasurements] = useState<{
    distance: string;
    area: string;
  }>({
    distance: '',
    area: ''
  });

  // 測量工具狀態
  const [isDrawing, setIsDrawing] = useState(false);
  const [currentPath, setCurrentPath] = useState<any>(null);
  const [drawnItems, setDrawnItems] = useState<any[]>([]);
  const [measurementMarkers, setMeasurementMarkers] = useState<any[]>([]);

  useEffect(() => {
    if (!map) return;

    // 清理之前的事件監聽器
    map.off('click');
    map.off('mousemove');

    if (activeTool === 'distance') {
      enableDistanceMeasurement();
    } else if (activeTool === 'area') {
      enableAreaMeasurement();
    } else if (activeTool === 'marker') {
      enableMarkerTool();
    }

    return () => {
      if (map) {
        map.off('click');
        map.off('mousemove');
      }
    };
  }, [map, activeTool]);

  // 當工具切換時，重置繪圖狀態
  useEffect(() => {
    setIsDrawing(false);
  }, [activeTool]);

  // 距離測量工具
  const enableDistanceMeasurement = () => {
    if (!map || !L) return;

    let points: any[] = [];
    let polyline: any = null;
    let markers: any[] = [];

    const onClick = (e: any) => {
      points.push(e.latlng);

      // 添加標記
      const marker = L.marker(e.latlng).addTo(map);
      markers.push(marker);

      // 將標記添加到狀態中以便清除
      setMeasurementMarkers(prev => [...prev, marker]);

      if (points.length > 1) {
        // 移除舊的線條
        if (polyline) {
          map.removeLayer(polyline);
          // 從繪製項目中移除舊線條
          setDrawnItems(prev => prev.filter(item => item !== polyline));
        }

        // 繪製新的線條
        polyline = L.polyline(points, { color: '#ff0000', weight: 3 }).addTo(map);

        // 將新線條添加到繪製項目中
        setDrawnItems(prev => [...prev, polyline]);

        // 計算總距離
        let totalDistance = 0;
        for (let i = 1; i < points.length; i++) {
          totalDistance += points[i - 1].distanceTo(points[i]);
        }

        setMeasurements(prev => ({
          ...prev,
          distance: `${(totalDistance / 1000).toFixed(2)} km`
        }));
      }
    };

    map.on('click', onClick);
    setIsDrawing(true);
  };

  // 面積測量工具
  const enableAreaMeasurement = () => {
    if (!map || !L) return;

    let points: any[] = [];
    let polygon: any = null;
    let markers: any[] = [];

    const onClick = (e: any) => {
      points.push(e.latlng);

      // 添加標記
      const marker = L.marker(e.latlng).addTo(map);
      markers.push(marker);

      // 將標記添加到狀態中以便清除
      setMeasurementMarkers(prev => [...prev, marker]);

      if (points.length > 2) {
        // 移除舊的多邊形
        if (polygon) {
          map.removeLayer(polygon);
          // 從繪製項目中移除舊多邊形
          setDrawnItems(prev => prev.filter(item => item !== polygon));
        }

        // 繪製新的多邊形
        polygon = L.polygon(points, {
          color: '#00ff00',
          weight: 2,
          fillOpacity: 0.2
        }).addTo(map);

        // 將新多邊形添加到繪製項目中
        setDrawnItems(prev => [...prev, polygon]);

        // 計算面積 - 使用 Leaflet 內建的面積計算
        const area = calculatePolygonArea(points);

        setMeasurements(prev => ({
          ...prev,
          area: `${(area / 10000).toFixed(2)} 公頃`
        }));
      }
    };

    map.on('click', onClick);
    setIsDrawing(true);
  };

  // 計算多邊形面積的函數（使用球面幾何）
  const calculatePolygonArea = (latlngs: any[]) => {
    if (!latlngs || latlngs.length < 3) return 0;

    const earthRadius = 6371000; // 地球半徑（米）
    let area = 0;

    // 將經緯度轉換為弧度
    const toRad = (deg: number) => deg * Math.PI / 180;

    for (let i = 0; i < latlngs.length; i++) {
      const j = (i + 1) % latlngs.length;
      const lat1 = toRad(latlngs[i].lat);
      const lat2 = toRad(latlngs[j].lat);
      const lng1 = toRad(latlngs[i].lng);
      const lng2 = toRad(latlngs[j].lng);

      area += (lng2 - lng1) * (2 + Math.sin(lat1) + Math.sin(lat2));
    }

    area = Math.abs(area * earthRadius * earthRadius / 2);
    return area;
  };

  // 標記工具
  const enableMarkerTool = () => {
    if (!map || !L) return;

    const onClick = (e: any) => {
      const marker = L.marker(e.latlng)
        .addTo(map)
        .bindPopup(`座標: ${e.latlng.lat.toFixed(6)}, ${e.latlng.lng.toFixed(6)}`)
        .openPopup();

      // 將標記添加到繪製項目中以便清除
      setDrawnItems(prev => [...prev, marker]);
    };

    map.on('click', onClick);
  };

  // 清除所有繪製項目
  const clearAll = () => {
    if (!map) return;

    // 清除所有繪製的線條和多邊形
    drawnItems.forEach(item => {
      if (map.hasLayer(item)) {
        map.removeLayer(item);
      }
    });

    // 清除所有測量標記
    measurementMarkers.forEach(marker => {
      if (map.hasLayer(marker)) {
        map.removeLayer(marker);
      }
    });

    // 重置所有狀態
    setDrawnItems([]);
    setMeasurementMarkers([]);
    setMeasurements({ distance: '', area: '' });
    setActiveTool(null);
    setIsDrawing(false);

    // 移除地圖事件監聽器
    map.off('click');
  };

  // 工具按鈕配置
  const tools = [
    {
      id: 'distance',
      name: '距離測量',
      icon: Ruler,
      color: 'bg-blue-600 hover:bg-blue-700',
      description: '點擊地圖測量距離'
    },
    {
      id: 'area',
      name: '面積測量',
      icon: Square,
      color: 'bg-green-600 hover:bg-green-700',
      description: '點擊地圖測量面積'
    },
    {
      id: 'marker',
      name: '標記點',
      icon: MapPin,
      color: 'bg-purple-600 hover:bg-purple-700',
      description: '在地圖上添加標記'
    }
  ];

  return (
    <div className="bg-white p-4 rounded-lg shadow-md border border-gray-200">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">地圖工具</h3>

      {/* 工具按鈕 */}
      <div className="space-y-3 mb-4">
        {tools.map((tool) => {
          const Icon = tool.icon;
          const isActive = activeTool === tool.id;

          return (
            <button
              key={tool.id}
              onClick={() => setActiveTool(isActive ? null : tool.id)}
              className={`w-full px-4 py-2 text-white rounded-md transition-colors flex items-center ${isActive
                ? 'bg-gray-800'
                : tool.color
                }`}
              title={tool.description}
            >
              <Icon className="w-4 h-4 mr-2" />
              {tool.name}
              {isActive && <span className="ml-auto text-xs">使用中</span>}
            </button>
          );
        })}
      </div>

      {/* 測量結果 */}
      {(measurements.distance || measurements.area) && (
        <div className="mb-4 p-3 bg-gray-50 rounded-lg">
          <h4 className="text-sm font-medium text-gray-900 mb-2">測量結果</h4>
          {measurements.distance && (
            <div className="text-sm text-gray-600">
              距離: <span className="font-medium text-blue-600">{measurements.distance}</span>
            </div>
          )}
          {measurements.area && (
            <div className="text-sm text-gray-600">
              面積: <span className="font-medium text-green-600">{measurements.area}</span>
            </div>
          )}
        </div>
      )}

      {/* 控制按鈕 */}
      <div className="space-y-2">
        <button
          onClick={clearAll}
          className="w-full px-4 py-2 bg-red-100 text-red-700 rounded-md hover:bg-red-200 transition-colors flex items-center"
        >
          <RotateCcw className="w-4 h-4 mr-2" />
          清除全部
        </button>

        <button
          onClick={() => {
            if (map) {
              // 簡單的截圖功能（需要額外的庫支援）
              alert('截圖功能需要額外實現');
            }
          }}
          className="w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors flex items-center"
        >
          <Download className="w-4 h-4 mr-2" />
          匯出地圖
        </button>
      </div>

      {/* 使用說明 */}
      {activeTool && (
        <div className="mt-4 p-3 bg-blue-50 rounded-lg">
          <p className="text-xs text-blue-800">
            {activeTool === 'distance' && '點擊地圖上的點來測量距離，點擊多個點可測量折線距離。'}
            {activeTool === 'area' && '點擊地圖上的點來測量面積，至少需要3個點形成多邊形。'}
            {activeTool === 'marker' && '點擊地圖任意位置添加標記點。'}
          </p>
        </div>
      )}
    </div>
  );
}
