'use client'

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { 
  Eye, 
  EyeOff, 
  Settings, 
  ChevronDown, 
  ChevronRight,
  Map as MapIcon,
  Layers
} from 'lucide-react';
import { cn } from '@/lib/utils';

export interface LayerConfig {
  id: string;
  name: string;
  type: 'base' | 'overlay';
  isVisible: boolean;
  opacity?: number;
  description?: string;
}

interface LayerPanelProps {
  layers: LayerConfig[];
  onLayerToggle: (layerId: string, visible: boolean) => void;
  onOpacityChange: (layerId: string, opacity: number) => void;
  className?: string;
}

export function LayerPanel({ 
  layers, 
  onLayerToggle, 
  onOpacityChange, 
  className 
}: LayerPanelProps) {
  const [expandedLayers, setExpandedLayers] = useState<Set<string>>(new Set());
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set(['base', 'overlay']));

  const toggleLayerExpanded = (layerId: string) => {
    const newExpanded = new Set(expandedLayers);
    if (newExpanded.has(layerId)) {
      newExpanded.delete(layerId);
    } else {
      newExpanded.add(layerId);
    }
    setExpandedLayers(newExpanded);
  };

  const toggleGroupExpanded = (groupType: string) => {
    const newExpanded = new Set(expandedGroups);
    if (newExpanded.has(groupType)) {
      newExpanded.delete(groupType);
    } else {
      newExpanded.add(groupType);
    }
    setExpandedGroups(newExpanded);
  };

  const baseLayers = layers.filter(layer => layer.type === 'base');
  const overlayLayers = layers.filter(layer => layer.type === 'overlay');

  const renderLayerGroup = (groupLayers: LayerConfig[], groupType: 'base' | 'overlay', title: string, icon: React.ReactNode) => {
    const isExpanded = expandedGroups.has(groupType);
    
    return (
      <div className="space-y-2">
        <Button
          variant="ghost"
          className="w-full justify-start p-2 h-auto"
          onClick={() => toggleGroupExpanded(groupType)}
        >
          {isExpanded ? <ChevronDown className="h-4 w-4 mr-2" /> : <ChevronRight className="h-4 w-4 mr-2" />}
          {icon}
          <span className="ml-2 font-medium">{title}</span>
          <span className="ml-auto text-xs text-muted-foreground">
            {groupLayers.filter(l => l.isVisible).length}/{groupLayers.length}
          </span>
        </Button>
        
        {isExpanded && (
          <div className="ml-4 space-y-2">
            {groupLayers.map((layer) => (
              <div key={layer.id} className="space-y-2">
                <div className="flex items-center justify-between p-2 rounded-md hover:bg-muted/50">
                  <div className="flex items-center space-x-2 flex-1">
                    <Switch
                      checked={layer.isVisible}
                      onCheckedChange={(checked) => onLayerToggle(layer.id, checked)}
                    />
                    <div className="flex-1">
                      <div className="text-sm font-medium">{layer.name}</div>
                      {layer.description && (
                        <div className="text-xs text-muted-foreground">{layer.description}</div>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0"
                      onClick={() => onLayerToggle(layer.id, !layer.isVisible)}
                    >
                      {layer.isVisible ? <Eye className="h-3 w-3" /> : <EyeOff className="h-3 w-3" />}
                    </Button>
                    
                    {layer.type === 'overlay' && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0"
                        onClick={() => toggleLayerExpanded(layer.id)}
                      >
                        <Settings className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                </div>
                
                {/* 透明度控制 */}
                {layer.type === 'overlay' && expandedLayers.has(layer.id) && (
                  <div className="ml-6 p-3 bg-muted/30 rounded-md space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-xs font-medium">透明度</span>
                      <span className="text-xs text-muted-foreground">
                        {Math.round((layer.opacity || 1) * 100)}%
                      </span>
                    </div>
                    <Slider
                      value={[(layer.opacity || 1) * 100]}
                      onValueChange={([value]) => onOpacityChange(layer.id, value / 100)}
                      max={100}
                      min={0}
                      step={5}
                      className="w-full"
                    />
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };

  return (
    <Card className={cn("w-80", className)}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center text-base">
          <Layers className="h-4 w-4 mr-2" />
          圖層控制
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {renderLayerGroup(baseLayers, 'base', '底圖圖層', <MapIcon className="h-4 w-4" />)}
        
        {baseLayers.length > 0 && overlayLayers.length > 0 && (
          <Separator />
        )}
        
        {renderLayerGroup(overlayLayers, 'overlay', '疊加圖層', <Layers className="h-4 w-4" />)}
        
        {layers.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <Layers className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">暫無可用圖層</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
