'use client'

import React, { useEffect, useRef, useState, useMemo } from 'react';
import dynamic from 'next/dynamic';
import { MapContainer, TileLayer, LayersControl, useMap, ImageOverlay } from 'react-leaflet';
import { mapConfig, type TileLayerConfig, type OverlayLayerConfig } from '@/config/map.config';
import 'leaflet/dist/leaflet.css';

import L from 'leaflet';
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
	iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
	iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
	shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

interface LeafletMapProps {
	height?: string;
	onMapReady?: (map: L.Map) => void;
	enabledLayers?: string[];
	children?: React.ReactNode;
}

// 地圖控制組件
function MapController({ onMapReady }: { onMapReady?: (map: L.Map) => void }) {
	const map = useMap();

	useEffect(() => {
		if (map && onMapReady) {
			onMapReady(map);
		}
	}, [map, onMapReady]);

	return null;
}

export function LeafletMap({ height = '400px', onMapReady, enabledLayers = ['century_map'], children }: LeafletMapProps) {
	const [map, setMap] = useState<L.Map | null>(null);

	const mapRef = useRef<L.Map | null>(null);
	const bounds = useMemo<L.LatLngBoundsExpression>(
		() => [
			[22.1, 120.25], // SW
			[22.95, 121.1], // NE
		],
		[]
	);

	const [opacity, setOpacity] = useState(0.6);

	const handleMapReady = (mapInstance: L.Map) => {
		setMap(mapInstance);
		mapRef.current = mapInstance;
		if (onMapReady) {
			onMapReady(mapInstance);
		}
	};

	// 渲染底圖圖層
	const renderBaseLayers = () => {
		return mapConfig.tileLayers.map((layer: TileLayerConfig) => (
			<LayersControl.BaseLayer key={layer.id} name={layer.name} checked={layer.isDefault}>
				<TileLayer url={layer.url} attribution={layer.attribution} maxZoom={layer.maxZoom} />
			</LayersControl.BaseLayer>
		));
	};

	// 渲染疊加圖層
	const renderOverlayLayers = () => {
		return mapConfig.overlayLayers
			.filter((layer: OverlayLayerConfig) => enabledLayers.includes(layer.id))
			.map((layer: OverlayLayerConfig) => (
				<LayersControl.Overlay key={layer.id} name={layer.name} checked={layer.isVisible}>
					{/* 根據 layer.type 渲染不同類型的圖層 */}
					{layer.type === 'image' && (
						<ImageOverlay url={layer.url} bounds={bounds} opacity={opacity} zIndex={500} interactive={false} />
					)}
					{layer.type === 'tile' && (
						<TileLayer
							url={layer.token ? `${layer.url}?token=${layer.token}` : layer.url}
							attribution={layer.attribution}
							minZoom={layer.minZoom}
							maxZoom={layer.maxZoom}
							bounds={
								layer.bounds
									? [
											[layer.bounds[0][0], layer.bounds[0][1]],
											[layer.bounds[1][0], layer.bounds[1][1]],
									  ]
									: undefined
							}
							opacity={opacity}
							zIndex={500}
						/>
					)}
				</LayersControl.Overlay>
			));
	};

	return (
		<div style={{ height, width: '100%', position: 'relative', zIndex: 1 }}>
			<MapContainer
				center={mapConfig.defaultCenter}
				zoom={mapConfig.defaultZoom}
				style={{ height: '100%', width: '100%', zIndex: 1 }}
				zoomControl={mapConfig.mapOptions.zoomControl}
				attributionControl={mapConfig.mapOptions.attributionControl}
				scrollWheelZoom={mapConfig.mapOptions.scrollWheelZoom}
				doubleClickZoom={mapConfig.mapOptions.doubleClickZoom}
				dragging={mapConfig.mapOptions.dragging}
			>
				<MapController onMapReady={handleMapReady} />

				<LayersControl position="topright">
					{renderBaseLayers()}
					{renderOverlayLayers()}
				</LayersControl>

				{/* 熱力圖層 */}
				{/* <HeatmapLayer
          data={heatmapData}
          isVisible={heatmapVisible}
          options={heatmapOptions}
        /> */}

				{/* 其他功能圖層，如熱力圖等... */}
				{children}
			</MapContainer>
		</div>
	);
}

// 使用 dynamic import 避免 SSR 問題
export default dynamic(() => Promise.resolve(LeafletMap), {
  ssr: false,
  loading: () => (
    <div 
      style={{ height: '400px' }} 
      className="bg-gray-100 flex items-center justify-center rounded-lg"
    >
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
        <p className="text-gray-500">地圖載入中...</p>
      </div>
    </div>
  )
});
