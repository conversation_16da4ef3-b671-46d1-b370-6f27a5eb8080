// Leaflet 地圖配置文件
export interface MapConfig {
  defaultCenter: [number, number];
  defaultZoom: number;
  minZoom: number;
  maxZoom: number;
  tileLayers: TileLayerConfig[];
  overlayLayers: OverlayLayerConfig[];
  mapOptions: {
    zoomControl: boolean;
    attributionControl: boolean;
    scrollWheelZoom: boolean;
    doubleClickZoom: boolean;
    dragging: boolean;
  };
}

export interface TileLayerConfig {
  id: string;
  name: string;
  url: string;
  attribution: string;
  maxZoom: number;
  isDefault?: boolean;
}

export interface OverlayLayerConfig {
	id: string;
	name: string;
	type: 'geojson' | 'wms' | 'marker' | 'image' | 'tile';
	url: string;
	// data?: any;
	style?: StyleConfig;
	isVisible?: boolean;
	attribution?: string;
	minZoom?: number;
	maxZoom?: number;
	bounds?: [[number, number], [number, number]]; // 瓦片圖層的邊界
	token?: string; // API token
	keepBuffer?: number; // 保留更多瓦片在記憶體中
	updateWhenZooming?: boolean; // 縮放時不立即更新
	updateWhenIdle?: boolean; // 只在靜止時更新
	maxNativeZoom?: number; // 設定最大原生縮放
	errorTileUrl?: string; // 錯誤時顯示的圖像
}

export interface StyleConfig {
	color?: string;
	weight?: number;
	opacity?: number;
	fillOpacity?: number;
}

// 台灣地圖配置
export const mapConfig: MapConfig = {
	// 預設中心點：屏東市中心
	defaultCenter: [22.6761, 120.4943],
	defaultZoom: 10,
	minZoom: 6,
	maxZoom: 18,

	// 底圖圖層配置
	tileLayers: [
		{
			id: 'osm',
			name: 'OpenStreetMap',
			url: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
			attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
			maxZoom: 19,
			isDefault: true,
		},
		{
			id: 'satellite',
			name: '衛星影像',
			url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
			attribution:
				'Tiles &copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community',
			maxZoom: 18,
		},
		{
			id: 'terrain',
			name: '地形圖',
			url: 'https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png',
			attribution:
				'Map data: &copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors, <a href="http://viewfinderpanoramas.org">SRTM</a> | Map style: &copy; <a href="https://opentopomap.org">OpenTopoMap</a> (<a href="https://creativecommons.org/licenses/by-sa/3.0/">CC-BY-SA</a>)',
			maxZoom: 17,
		},
		{
			id: 'cartodb',
			name: 'CartoDB 淺色',
			url: 'https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png',
			attribution:
				'&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>',
			maxZoom: 19,
		},
	],

	// 疊加圖層配置
	overlayLayers: [
		// {
		//   id: 'land_use',
		//   name: '土地使用分區',
		//   type: 'geojson',
		//   isVisible: true,
		//   style: {
		//     color: '#3388ff',
		//     weight: 2,
		//     opacity: 0.8,
		//     fillOpacity: 0.3
		//   },
		//   url: ''
		// },
		{
			id: 'century_map',
			name: '百年地圖',
			type: 'tile',
			isVisible: true,
			url: 'https://api2.daoyidh.com/land-fs2/tiles/{z}/{x}/{y}.png',
			attribution: '百年歷史地籍圖資料',
			minZoom: 8,
			maxZoom: 19,
			bounds: [
				[22.4, 120.45], // 南西角 (最小緯度, 最小經度)
				[22.78, 120.7], // 北東角 (最大緯度, 最大經度)
			],
			token: process.env.NEXT_PUBLIC_CENTURY_MAP_TOKEN,
			keepBuffer: 4, // 保留更多瓦片在記憶體中
			updateWhenZooming: false, // 縮放時不立即更新
			updateWhenIdle: true, // 只在靜止時更新
			maxNativeZoom: 19, // 設定最大原生縮放
			errorTileUrl: '...',
		},
	],

	// 地圖選項
	mapOptions: {
		zoomControl: true,
		attributionControl: true,
		scrollWheelZoom: true,
		doubleClickZoom: true,
		dragging: true,
	},
};

// 地圖工具配置
export const mapTools = {
	measurement: {
		distance: {
			enabled: true,
			color: '#ff0000',
			weight: 3,
		},
		area: {
			enabled: true,
			color: '#00ff00',
			weight: 2,
			fillOpacity: 0.2,
		},
	},
	drawing: {
		marker: {
			enabled: true,
		},
		polyline: {
			enabled: true,
			color: '#0000ff',
			weight: 3,
		},
		polygon: {
			enabled: true,
			color: '#ff00ff',
			weight: 2,
			fillOpacity: 0.3,
		},
	},
};

// 台灣常用座標系統
export const coordinateSystems = {
	WGS84: 'EPSG:4326',
	TWD97: 'EPSG:3826',
	TWD67: 'EPSG:3828',
};

// 預設標記樣式
export const markerStyles = {
	default: {
		iconUrl: '/icons/marker-icon.png',
		shadowUrl: '/icons/marker-shadow.png',
		iconSize: [25, 41],
		iconAnchor: [12, 41],
		popupAnchor: [1, -34],
		shadowSize: [41, 41],
	},
	highlight: {
		iconUrl: '/icons/marker-icon-red.png',
		shadowUrl: '/icons/marker-shadow.png',
		iconSize: [25, 41],
		iconAnchor: [12, 41],
		popupAnchor: [1, -34],
		shadowSize: [41, 41],
	},
};

// 地圖功能提示文字
export const mapTips = {
	'surname-distribution': '姓氏分布功能已啟用，您可以拖動時間軸來查看不同年代的姓氏分布變化。',
	'land-location': '土地坐落功能已啟用，您可以縮放地圖來在地段cluster和個別地號之間切換。',
	'transaction-heatmap': '土地交易次數功能已啟用，您可以調整時間軸範圍來查看特定時期的交易熱度。',
	'search-radius': '搜索半徑功能已啟用，請點擊地圖上的任意位置開始搜索。',
	'land-type-distribution': '各種地目分布功能已啟用，不同地目類型會以不同的顏色和形狀顯示。',
};

// 時間軸設定
export const timelineConfig = {
	minYear: 1900,
	maxYear: 1950,
	startYear: 1900,
	endYear: 1950,
	currentYear: 1925,
};
