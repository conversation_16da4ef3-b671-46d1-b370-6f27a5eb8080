// // 熱力圖 API 相關函數
// import { HeatmapDataPoint } from '@/components/map/HeatmapLayer';
// import { API_ENDPOINTS } from './api';

// // API 回應的數據結構（根據您的需求調整）
// export interface HeatmapApiResponse {
//   data: {
//     id: string;
//     lat: number;
//     lng: number;
//     yearCounts: string; // 該地番個別權利變更年份的交易次數
//     eventTimes?: string; // 每次權利變更變更的權利登記時間
//   }[];
//   total: string;
// }

// type Series = { yearly: Map<number, number>; times: string[] }; // times 是原始 time IRI/字串

// function parseYearCounts(s: string): Map<number, number> {
//   const m = new Map<number, number>();
//   if (!s) return m;
//   for (const p of s.split("|")) {
//     const [y, c] = p.split(":");
//     const yi = +y, ci = +c;
//     if (!Number.isNaN(yi) && !Number.isNaN(ci)) m.set(yi, ci);
//   }
//   return m;
// }

// const yearFromIRI = (iri: string) =>
//   parseInt(iri.replace(/^.*DAE/, "").slice(0, 4), 10);

// function heatValue(s: Series, start: number, current: number): number {
//   let sum = 0;
//   for (let y = start; y <= current; y++) sum += s.yearly.get(y) ?? 0;
//   return sum;
// }

// function filterEventTimes(s: Series, start: number, end: number): string[] {
//   return s.times.filter(t => {
//     const y = yearFromIRI(t);
//     return y >= start && y <= end;
//   });
// }


// // 將 API 數據轉換為熱力圖數據格式
// export function transformApiDataToHeatmap(apiData: HeatmapApiResponse['data']): HeatmapDataPoint[] {
//   if (!apiData || apiData.length === 0) {
//     return [];
//   }

//   // 找出最大交易次數用於歸一化
//   const maxTransactionCount = Math.max(...apiData.map(item => item.transactionCount));
  
//   return apiData.map(item => ({
//     lat: item.lat,
//     lng: item.lng,
//     intensity: maxTransactionCount > 0 ? item.transactionCount / maxTransactionCount : 0
//   }));
// }

// // 真實 API 調用函數（之後實現）
// export async function fetchRealHeatmapData(params?: {
//   startYear?: number;
//   endYear?: number;
//   region?: string;
//   limit?: number;
// }): Promise<HeatmapApiResponse> {
//   try {
//     // 這裡會是真實的 API 端點
//     const apiUrl = API_ENDPOINTS.GIS_TRANSACTIONS_DATA;
//     const response = await fetch(apiUrl);
//     console.log('response', response);
    
//     if (!response.ok) {
//       throw new Error(`API 請求失敗: ${response.status}`);
//     }

//     const data: HeatmapApiResponse = await response.json();
//     return data;
//   } catch (error) {
//     console.error('獲取熱力圖數據失敗:', error);
    
//     // 如果 API 失敗，回退到模擬數據
//     return await fetchHeatmapData(params);
//   }
// }

// // 根據時間範圍過濾數據
// export function filterHeatmapDataByTimeRange(
//   data: HeatmapDataPoint[],
//   startYear: number,
//   endYear: number
// ): HeatmapDataPoint[] {
//   // 這裡可以根據實際的時間戳字段進行過濾
//   // 目前返回所有數據，因為模擬數據沒有時間範圍
//   return data;
// }

// // 根據強度範圍過濾數據
// export function filterHeatmapDataByIntensity(
//   data: HeatmapDataPoint[],
//   minIntensity: number,
//   maxIntensity: number
// ): HeatmapDataPoint[] {
//   return data.filter(point => 
//     point.intensity >= minIntensity && point.intensity <= maxIntensity
//   );
// }

// // 計算熱力圖統計信息
// export function calculateHeatmapStats(data: HeatmapDataPoint[]) {
//   if (data.length === 0) {
//     return {
//       totalPoints: 0,
//       avgIntensity: 0,
//       maxIntensity: 0,
//       minIntensity: 0
//     };
//   }

//   const intensities = data.map(point => point.intensity);
//   const totalPoints = data.length;
//   const avgIntensity = intensities.reduce((sum, intensity) => sum + intensity, 0) / totalPoints;
//   const maxIntensity = Math.max(...intensities);
//   const minIntensity = Math.min(...intensities);

//   return {
//     totalPoints,
//     avgIntensity: Number(avgIntensity.toFixed(3)),
//     maxIntensity: Number(maxIntensity.toFixed(3)),
//     minIntensity: Number(minIntensity.toFixed(3))
//   };
// }


// // 模擬 API 調用函數
// export async function fetchHeatmapData(params?: {
//   startYear?: number;
//   endYear?: number;
//   region?: string;
//   limit?: number;
// }): Promise<HeatmapApiResponse> {
//   // 模擬 API 延遲
//   await new Promise(resolve => setTimeout(resolve, 1000));

//   // 模擬數據生成
//   const mockData: HeatmapApiResponse['data'] = [];
//   const centerLat = 22.5;
//   const centerLng = 120.6;
//   const count = params?.limit || 100;

//   for (let i = 0; i < count; i++) {
//     const lat = centerLat + (Math.random() - 0.5) * 0.5;
//     const lng = centerLng + (Math.random() - 0.5) * 0.5;
//     const transactionCount = Math.floor(Math.random() * 50) + 1;
    
//     mockData.push({
//       id: `point_${i}`,
//       lat,
//       lng,
//       transactionCount,
//       value: Math.random() * 1000000,
//       timestamp: new Date().toISOString(),
//       region: `區域_${Math.floor(Math.random() * 10) + 1}`
//     });
//   }

//   return {
//     success: true,
//     data: mockData,
//     total: mockData.length,
//     message: '數據獲取成功'
//   };
// }


// 熱力圖 API 相關函數
import { HeatmapDataPoint } from '@/components/map/Layer/HeatmapLayer';
import { API_ENDPOINTS } from './api';

// API 回應的數據結構
export interface HeatmapApiResponse {
  data: HeatmapApiItem[];
  total: string;
  success: boolean;
}

export interface HeatmapApiItem {
  id: string;
  lat: number;
  lng: number;
  yearCounts: string;   // "1906:2|1907:0|1908:5|..."
  eventTimes: string;  // "http://...#DAE19060101|http://...#DAE19080312|..."
}

type Series = { yearly: Map<number, number>; times: string[] }; // times 為原始 time IRI/字串

function parseYearCounts(s: string): Map<number, number> {
  const m = new Map<number, number>();
  if (!s) return m;
  for (const p of s.split('|')) {
    if (!p) continue;
    const [y, c] = p.split(':');
    const yi = Number(y), ci = Number(c);
    if (Number.isFinite(yi) && Number.isFinite(ci)) m.set(yi, ci);
  }
  return m;
}

const yearFromIRI = (iri: string) =>
  parseInt(iri.replace(/^.*DAE/, '').slice(0, 4), 10);

function parseTimes(times?: string): string[] {
  if (!times) return [];
  return times.split('|').filter(Boolean);
}

function heatValue(s: Series, start: number, current: number): number {
  // 區間 [start, current] 含邊界求和
  let sum = 0;
  for (let y = start; y <= current; y++) sum += s.yearly.get(y) ?? 0;
  return sum;
}

function filterEventTimes(s: Series, start: number, end: number): string[] {
  return s.times.filter(t => {
    const y = yearFromIRI(t);
    return Number.isFinite(y) && y >= start && y <= end;
  });
}

// 建立每個點位的時間序列索引
function buildSeries(item: HeatmapApiItem): Series {
  return {
    yearly: parseYearCounts(item.yearCounts),
    times: parseTimes(item.eventTimes),
  };
}

// ---- 1) 先過濾並裁切 API 資料 ----
export function filterApiDataByYearRange(
  apiData: HeatmapApiResponse['data'],
  startYear: number,
  endYear: number
): HeatmapApiResponse['data'] {
  const inRange = (y: number) => y >= startYear && y <= endYear;

  return apiData
    .map(item => {
      // 裁切 yearCounts
      const yc = parseYearCounts(item.yearCounts);
      const kept: Array<[number, number]> = [];
      let sum = 0;
      yc.forEach((cnt, y) => {
        if (inRange(y)) {
          kept.push([y, cnt]);
          sum += cnt;
        }
      });

      // 裁切 eventTimes
      const times = parseTimes(item.eventTimes);
      const keptTimes = times.filter(t => {
        const y = yearFromIRI(t);
        return Number.isFinite(y) && inRange(y);
      });

      // 若區間內完全沒有事件則丟棄
      if (sum === 0 && keptTimes.length === 0) return null;

      // 重建 yearCounts 字串（僅保留區間內）
      kept.sort((a, b) => a[0] - b[0]);
      const yearCounts = kept.map(([y, c]) => `${y}:${c}`).join('|');

      return {
        ...item,
        yearCounts,
        eventTimes: keptTimes.length ? keptTimes.join('|') : undefined,
      };
    })
    .filter((x): x is HeatmapApiItem => x !== null);
}

// ---- 2) 轉為熱力圖（此時資料已被裁切到 [startYear, endYear]） ----
export function transformApiDataToHeatmap(
  filteredApiData: HeatmapApiResponse['data'],
  startYear: number,
  currentYear: number,
  endYear: number // 用於夾限 currentYear
): HeatmapDataPoint[] {
  if (!filteredApiData?.length) return [];

  const cur = Math.max(startYear, Math.min(currentYear, endYear));

  // 先算每點原始值（[startYear, cur] 的總事件數）
  const raw = filteredApiData.map(item => {
    const series: Series = {
      yearly: parseYearCounts(item.yearCounts),
      times: parseTimes(item.eventTimes),
    };
    const v = heatValue(series, startYear, cur);
    return { lat: item.lat, lng: item.lng, value: v };
  });

  // 歸一化
  const maxV = Math.max(0, ...raw.map(r => r.value));
  const denom = maxV > 0 ? maxV : 1;

  return raw.map(r => ({
    lat: r.lat,
    lng: r.lng,
    intensity: r.value / denom,
  }));
}


/**
 * 依時間範圍回傳每個點對應的事件時間（用於點選 tooltip 或細節）
 * 不改變 HeatmapDataPoint 結構，僅提供輔助結果。
 */
export function extractEventTimesByRange(
  apiData: HeatmapApiResponse['data'],
  startYear: number,
  endYear: number
): Record<string, string[]> {
  const out: Record<string, string[]> = {};
  for (const item of apiData) {
    const series = buildSeries(item);
    out[item.id] = filterEventTimes(series, startYear, endYear);
  }
  return out;
}

/**
 * 真實 API 調用，將查詢參數串到 URL（可搭配 bbox、limit 等）
 */
export async function fetchRealHeatmapData(params?: {
  startYear?: number;   // 可選，後端若不需要可忽略
  endYear?: number;     // 可選
  region?: string;      // 可選
  limit?: number;       // 可選
}): Promise<HeatmapApiResponse> {
  try {
    const url = new URL(API_ENDPOINTS.GIS_TRANSACTIONS_DATA, window.location.origin);
    // if (params?.startYear != null) url.searchParams.set('startYear', String(params.startYear));
    // if (params?.endYear != null)   url.searchParams.set('endYear', String(params.endYear));
    // if (params?.region)            url.searchParams.set('region', params.region);
    // if (params?.limit != null)     url.searchParams.set('limit', String(params.limit));

    const response = await fetch(url.toString(), { method: 'GET' });
    if (!response.ok) throw new Error(`API 請求失敗: ${response.status}`);
    const data: HeatmapApiResponse = await response.json();
    return data;
  } catch (error) {
    console.error('獲取熱力圖數據失敗:', error);
    // 若你仍保留本地 mock，這裡可回退；否則丟出錯誤
    throw error;
  }
}

/**
 * 強度篩選（保留）
 */
export function filterHeatmapDataByIntensity(
  data: HeatmapDataPoint[],
  minIntensity: number,
  maxIntensity: number
): HeatmapDataPoint[] {
  return data.filter(p => p.intensity >= minIntensity && p.intensity <= maxIntensity);
}

/**
 * 統計（保留）
 */
export function calculateHeatmapStats(data: HeatmapDataPoint[]) {
  if (data.length === 0) {
    return { totalPoints: 0, avgIntensity: 0, maxIntensity: 0, minIntensity: 0 };
  }
  const intensities = data.map(p => p.intensity);
  const totalPoints = data.length;
  const avgIntensity = intensities.reduce((s, v) => s + v, 0) / totalPoints;
  const maxIntensity = Math.max(...intensities);
  const minIntensity = Math.min(...intensities);
  return {
    totalPoints,
    avgIntensity: Number(avgIntensity.toFixed(3)),
    maxIntensity: Number(maxIntensity.toFixed(3)),
    minIntensity: Number(minIntensity.toFixed(3)),
  };
}
