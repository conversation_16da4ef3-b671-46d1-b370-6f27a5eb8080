'use client'

import { useEffect, useState, useMemo } from 'react';
import { useMap } from 'react-leaflet';
import { LandTypeData, landTypeConfig } from '@/lib/feature-data';
import { filterDataByBounds, sampleDataByZoom, debounce, type MapBounds } from '@/lib/map-performance';
import { getMaxPointsForZoom, shouldUseClustering, getViewportConfig, measurePerformance } from '@/config/map-performance.config';

interface LandTypeLayerProps {
	data: LandTypeData[];
	visible?: boolean;
}

// 根據形狀創建 SVG 圖標
function createShapeIcon(shape: string, color: string, size: number = 20): string {
	const halfSize = size / 2;

	switch (shape) {
		case 'circle':
			return `<circle cx="${halfSize}" cy="${halfSize}" r="${halfSize - 2}" fill="${color}" stroke="white" stroke-width="2"/>`;

		case 'square':
			return `<rect x="2" y="2" width="${size - 4}" height="${size - 4}" fill="${color}" stroke="white" stroke-width="2"/>`;

		case 'triangle':
			return `<polygon points="${halfSize},2 ${size - 2},${size - 2} 2,${
				size - 2
			}" fill="${color}" stroke="white" stroke-width="2"/>`;

		case 'star': {
			const points = [];
			for (let i = 0; i < 5; i++) {
				const angle = ((i * 144 - 90) * Math.PI) / 180;
				const outerRadius = halfSize - 2;
				const innerRadius = outerRadius * 0.4;

				// 外點
				points.push(`${halfSize + outerRadius * Math.cos(angle)},${halfSize + outerRadius * Math.sin(angle)}`);

				// 內點
				const innerAngle = (((i + 0.5) * 144 - 90) * Math.PI) / 180;
				points.push(`${halfSize + innerRadius * Math.cos(innerAngle)},${halfSize + innerRadius * Math.sin(innerAngle)}`);
			}
			return `<polygon points="${points.join(' ')}" fill="${color}" stroke="white" stroke-width="2"/>`;
		}

		case 'diamond':
			return `<polygon points="${halfSize},2 ${size - 2},${halfSize} ${halfSize},${
				size - 2
			} 2,${halfSize}" fill="${color}" stroke="white" stroke-width="2"/>`;

		default:
			return `<circle cx="${halfSize}" cy="${halfSize}" r="${halfSize - 2}" fill="${color}" stroke="white" stroke-width="2"/>`;
	}
}

// 創建聚合圖標
function createClusterIcon(landType: string, count: number, color: string): string {
	return `
    <div style="
      background: ${color};
      color: white;
      padding: 8px;
      border-radius: 50%;
      font-size: 12px;
      font-weight: bold;
      text-align: center;
      border: 3px solid white;
      box-shadow: 0 2px 8px rgba(0,0,0,0.3);
      min-width: 35px;
      min-height: 35px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      line-height: 1;
    ">
      <div style="font-size: 8px; margin-bottom: 1px;">${landType}</div>
      <div>${count}</div>
    </div>
  `;
}

export function LandTypeLayer({ data, visible = true }: LandTypeLayerProps) {
	const map = useMap();
	const [currentZoom, setCurrentZoom] = useState(10);
	const [mapBounds, setMapBounds] = useState<MapBounds | null>(null);

	// 使用 useMemo 優化數據處理
	const optimizedData = useMemo(() => {
		return measurePerformance('LandTypeLayer Data Processing', () => {
			if (!data.length || !mapBounds) return data;

			const viewportConfig = getViewportConfig('landType');

			// 1. 先根據視窗範圍過濾
			const boundsFiltered = viewportConfig.enabled ? filterDataByBounds(data, mapBounds, viewportConfig.bufferRatio) : data;

			// 2. 根據縮放層級進行採樣
			const maxPoints = getMaxPointsForZoom(currentZoom, 'landType');
			const sampled = sampleDataByZoom(boundsFiltered, currentZoom, maxPoints);

			return sampled;
		});
	}, [data, mapBounds, currentZoom]);

	// 防抖的邊界更新函數
	const debouncedUpdateBounds = useMemo(() => {
		const viewportConfig = getViewportConfig('landType');
		return debounce((bounds: MapBounds) => {
			setMapBounds(bounds);
		}, viewportConfig.updateDelay);
	}, []);

	// 監聽縮放和移動變化
	useEffect(() => {
		if (!map) return;

		// 設置初始值
		setCurrentZoom(map.getZoom());
		const bounds = map.getBounds();
		setMapBounds({
			north: bounds.getNorth(),
			south: bounds.getSouth(),
			east: bounds.getEast(),
			west: bounds.getWest(),
		});

		// 監聽縮放變化
		const handleZoomEnd = () => {
			const newZoom = map.getZoom();
			setCurrentZoom(newZoom);

			const bounds = map.getBounds();
			debouncedUpdateBounds({
				north: bounds.getNorth(),
				south: bounds.getSouth(),
				east: bounds.getEast(),
				west: bounds.getWest(),
			});
		};

		// 監聽移動變化
		const handleMoveEnd = () => {
			const bounds = map.getBounds();
			debouncedUpdateBounds({
				north: bounds.getNorth(),
				south: bounds.getSouth(),
				east: bounds.getEast(),
				west: bounds.getWest(),
			});
		};

		map.on('zoomend', handleZoomEnd);
		map.on('moveend', handleMoveEnd);

		return () => {
			map.off('zoomend', handleZoomEnd);
			map.off('moveend', handleMoveEnd);
		};
	}, [map, debouncedUpdateBounds]);

	useEffect(() => {
		if (!map) return;

		let landTypeLayerGroup: any = null;
		let isMounted = true;

		const loadLeaflet = async () => {
			const L = (await import('leaflet')).default;

			// 檢查組件是否仍然掛載
			if (!isMounted) return;

			landTypeLayerGroup = L.layerGroup();

			// 只有當可見且有數據時才創建標記
			if (visible && optimizedData.length > 0) {
				// 性能監控：只在開發環境下輸出
				if (process.env.NODE_ENV === 'development') {
					console.warn(`LandTypeLayer: 渲染 ${optimizedData.length}/${data.length} 個標記 (縮放: ${currentZoom})`);
				}

				// 根據縮放層級決定顯示方式
				if (shouldUseClustering(currentZoom, 'landType')) {
					// 拉遠時顯示地目類型聚合
					const landTypeGroups: { [key: string]: LandTypeData[] } = {};

					// 按地目類型分組
					optimizedData.forEach((item) => {
						if (!landTypeGroups[item.landType]) {
							landTypeGroups[item.landType] = [];
						}
						landTypeGroups[item.landType].push(item);
					});

					// 為每個地目類型創建聚合標記
					Object.entries(landTypeGroups).forEach(([landType, items]) => {
						if (items.length === 0) return;

						// 計算地目類型中心點
						const centerLat = items.reduce((sum, item) => sum + item.lat, 0) / items.length;
						const centerLng = items.reduce((sum, item) => sum + item.lng, 0) / items.length;

						// 獲取地目類型配置
						const config = landTypeConfig[landType as keyof typeof landTypeConfig] || {
							color: '#6b7280',
							shape: 'circle' as const,
							name: landType,
						};

						// 創建聚合圖標
						const clusterIcon = L.divIcon({
							html: createClusterIcon(landType, items.length, config.color),
							className: 'land-type-cluster-marker',
							iconSize: [40, 40],
							iconAnchor: [20, 20],
						});

						const marker = L.marker([centerLat, centerLng], { icon: clusterIcon });

						// 計算統計資訊
						const totalArea = items.reduce((sum, item) => sum + item.area, 0);
						const totalRent = items.reduce((sum, item) => sum + item.rent, 0);
						const avgArea = totalArea / items.length;
						const avgRent = totalRent / items.length;

						marker.bindPopup(`
              <div style="font-size: 14px;">
                <strong>${landType} 地目聚合</strong><br/>
                <strong>數量:</strong> ${items.length} 筆<br/>
                <strong>總面積:</strong> ${totalArea.toLocaleString()} (甲)<br/>
                <strong>平均面積:</strong> ${avgArea.toFixed(1)} (甲)<br/>
                <strong>總地租:</strong> ${totalRent.toLocaleString()} (圓)<br/>
                <strong>平均地租:</strong> ${avgRent.toFixed(0)} (圓)<br/>
                <em>放大地圖查看個別地號</em>
              </div>
            `);

						landTypeLayerGroup.addLayer(marker);
					});
				} else if (currentZoom <= 14) {
					// 中等縮放時顯示簡化標記（只顯示形狀，不顯示文字）
					optimizedData.forEach((item) => {
						const size = 16;
						const svgIcon = `
              <svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
                ${createShapeIcon(item.shape, item.color, size)}
              </svg>
            `;

						const landTypeIcon = L.divIcon({
							html: `
              <div style="
                display: flex;
                align-items: center;
                justify-content: center;
                filter: drop-shadow(0 1px 2px rgba(0,0,0,0.3));
              ">
                ${svgIcon}
              </div>
            `,
							className: 'land-type-marker-simple',
							iconSize: [size, size],
							iconAnchor: [size / 2, size / 2],
						});

						const marker = L.marker([item.lat, item.lng], { icon: landTypeIcon });

						marker.bindPopup(`
            <div style="font-size: 14px;">
              <strong>地目分布</strong><br/>
              <strong>地目:</strong> ${item.landType}<br/>
              <strong>地號:</strong> ${item.landNumber}<br/>
              <strong>面積:</strong> ${item.area.toLocaleString()} (甲)<br/>
              <strong>地租:</strong> ${item.rent.toLocaleString()} (圓)<br/>
              <strong>年份:</strong> ${item.year}
            </div>
          `);

						landTypeLayerGroup.addLayer(marker);
					});
				} else {
					// 拉近時顯示完整標記（形狀 + 文字）
					optimizedData.forEach((item) => {
						const size = 20;
						const svgIcon = `
            <svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
              ${createShapeIcon(item.shape, item.color, size)}
            </svg>
          `;

						const landTypeIcon = L.divIcon({
							html: `
            <div style="
              display: flex;
              flex-direction: column;
              align-items: center;
              filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
            ">
              ${svgIcon}
              <div style="
                background: rgba(255, 255, 255, 0.9);
                color: #374151;
                padding: 1px 3px;
                border-radius: 2px;
                font-size: 9px;
                font-weight: bold;
                margin-top: 1px;
                border: 1px solid rgba(0,0,0,0.1);
                white-space: nowrap;
              ">
                ${item.landType}
              </div>
            </div>
          `,
							className: 'land-type-marker',
							iconSize: [size + 8, size + 16],
							iconAnchor: [(size + 8) / 2, size + 16],
						});

						const marker = L.marker([item.lat, item.lng], { icon: landTypeIcon });

						marker.bindPopup(`
          <div style="font-size: 14px;">
            <strong>地目分布</strong><br/>
            <strong>地目:</strong> ${item.landType}<br/>
            <strong>地號:</strong> ${item.landNumber}<br/>
            <strong>面積:</strong> ${item.area.toLocaleString()} (甲)<br/>
            <strong>地租:</strong> ${item.rent.toLocaleString()} (圓)<br/>
            <strong>年份:</strong> ${item.year}
          </div>
        `);

						landTypeLayerGroup.addLayer(marker);
					});
				}
			}

			// 只有當可見時才添加到地圖
			if (visible && isMounted) {
				landTypeLayerGroup.addTo(map);
			}
		};

		loadLeaflet();

		return () => {
			isMounted = false;
			if (landTypeLayerGroup && map) {
				try {
					map.removeLayer(landTypeLayerGroup);
				} catch (error) {
					// 忽略移除圖層時的錯誤
					console.warn('Error removing land type layer:', error);
				}
			}
		};
	}, [map, optimizedData, visible, currentZoom]);

	return null;
}
