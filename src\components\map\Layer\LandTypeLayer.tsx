'use client'

import { useEffect } from 'react';
import { useMap } from 'react-leaflet';
import { LandTypeData } from '@/lib/feature-data';

interface LandTypeLayerProps {
  data: LandTypeData[];
  visible?: boolean;
}

// 根據形狀創建 SVG 圖標
function createShapeIcon(shape: string, color: string, size: number = 20): string {
  const halfSize = size / 2;
  
  switch (shape) {
    case 'circle':
      return `<circle cx="${halfSize}" cy="${halfSize}" r="${halfSize - 2}" fill="${color}" stroke="white" stroke-width="2"/>`;
    
    case 'square':
      return `<rect x="2" y="2" width="${size - 4}" height="${size - 4}" fill="${color}" stroke="white" stroke-width="2"/>`;
    
    case 'triangle':
      return `<polygon points="${halfSize},2 ${size - 2},${size - 2} 2,${size - 2}" fill="${color}" stroke="white" stroke-width="2"/>`;
    
    case 'star':
      {
      const points = [];
      for (let i = 0; i < 5; i++) {
        const angle = (i * 144 - 90) * Math.PI / 180;
        const outerRadius = halfSize - 2;
        const innerRadius = outerRadius * 0.4;
        
        // 外點
        points.push(`${halfSize + outerRadius * Math.cos(angle)},${halfSize + outerRadius * Math.sin(angle)}`);
        
        // 內點
        const innerAngle = ((i + 0.5) * 144 - 90) * Math.PI / 180;
        points.push(`${halfSize + innerRadius * Math.cos(innerAngle)},${halfSize + innerRadius * Math.sin(innerAngle)}`);
      }
      return `<polygon points="${points.join(' ')}" fill="${color}" stroke="white" stroke-width="2"/>`;
      }
    
    case 'diamond':
      return `<polygon points="${halfSize},2 ${size - 2},${halfSize} ${halfSize},${size - 2} 2,${halfSize}" fill="${color}" stroke="white" stroke-width="2"/>`;
    
    default:
      return `<circle cx="${halfSize}" cy="${halfSize}" r="${halfSize - 2}" fill="${color}" stroke="white" stroke-width="2"/>`;
  }
}

export function LandTypeLayer({ data, visible = true }: LandTypeLayerProps) {
  const map = useMap();

  useEffect(() => {
    if (!map) return;

    let landTypeLayerGroup: any = null;
    let isMounted = true;

    const loadLeaflet = async () => {
      const L = (await import('leaflet')).default;

      // 檢查組件是否仍然掛載
      if (!isMounted) return;

      landTypeLayerGroup = L.layerGroup();

      // 只有當可見且有數據時才創建標記
      if (visible && data.length > 0) {

    data.forEach((item) => {
      const size = 24;
      const svgIcon = `
        <svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
          ${createShapeIcon(item.shape, item.color, size)}
        </svg>
      `;

      const landTypeIcon = L.divIcon({
        html: `
          <div style="
            display: flex;
            flex-direction: column;
            align-items: center;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
          ">
            ${svgIcon}
            <div style="
              background: rgba(255, 255, 255, 0.9);
              color: #374151;
              padding: 1px 4px;
              border-radius: 3px;
              font-size: 10px;
              font-weight: bold;
              margin-top: 2px;
              border: 1px solid rgba(0,0,0,0.1);
            ">
              ${item.landType}
            </div>
          </div>
        `,
        className: 'land-type-marker',
        iconSize: [size + 10, size + 20],
        iconAnchor: [(size + 10) / 2, size + 20]
      });

      const marker = L.marker([item.lat, item.lng], { icon: landTypeIcon });

      marker.bindPopup(`
        <div style="font-size: 14px;">
          <strong>地目分布</strong><br/>
          <strong>地目:</strong> ${item.landType}<br/>
          <strong>地號:</strong> ${item.landNumber}<br/>
          <strong>面積:</strong> ${item.area.toLocaleString()} (甲)<br/>
          <strong>地租:</strong> ${item.rent.toLocaleString()} (圓)<br/>
          <strong>年份:</strong> ${item.year}
        </div>
      `);

        landTypeLayerGroup.addLayer(marker);
      });
      }

      // 只有當可見時才添加到地圖
      if (visible && isMounted) {
        landTypeLayerGroup.addTo(map);
      }
    };

    loadLeaflet();

    return () => {
      isMounted = false;
      if (landTypeLayerGroup && map) {
        try {
          map.removeLayer(landTypeLayerGroup);
        } catch (error) {
          // 忽略移除圖層時的錯誤
          console.warn('Error removing land type layer:', error);
        }
      }
    };
  }, [map, data, visible]);

  return null;
}
