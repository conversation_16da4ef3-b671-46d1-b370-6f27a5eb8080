"use client";
import React, {
  useRef,
  useMemo,
  useState,
  useEffect,
  useCallback,
} from "react";

// shadcn ui
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectItem,
  SelectValue,
  SelectContent,
  SelectTrigger,
} from "@/components/ui/select";

// lucide icons
import {
  RepeatIcon,
  Repeat1Icon,
  CirclePlayIcon,
  CirclePauseIcon,
} from "lucide-react";

// utils
import drawTimeLine from "../_utils/drawTimeLine";

// hooks
import debounce from "lodash-es/debounce";

// components
import YearInput from "./YearInput";

// local configs
const INTERVAL1X = 125;
const PLAYBACK_MODE = {
  ONCE: "once",
  REPEAT: "repeat",
};
const SPEED_OPTIONS = [
  { value: 0.25, label: "0.25x" },
  { value: 0.5, label: "0.50x" },
  { value: 0.75, label: "0.75x" },
  { value: 1, label: "1x" },
  { value: 2, label: "2x" },
  { value: 3, label: "3x" },
];
const DURATION_YEARS = 1;
const DEFAULT_TIMELINE_YEAR = { start: 1900, end: 1960 };

const TimeLine = ({
  restartTrigger,
  defaultYearRange,
}: {
  restartTrigger?: () => void;
  defaultYearRange?: number[];
}) => {
  const [year, setYear] = useState<{ start: number; end: number }>({
    start: defaultYearRange?.at(0) || DEFAULT_TIMELINE_YEAR.start,
    end: defaultYearRange?.at(-1) || DEFAULT_TIMELINE_YEAR.end,
  });
  const [anchorYear, setAnchorYear] = useState<number>(
    defaultYearRange?.at(0) || DEFAULT_TIMELINE_YEAR.start,
  );
  const [timeLineYear, setTimeLineYear] = useState<{
    start: number;
    end: number;
  }>({
    start: defaultYearRange?.at(0) || DEFAULT_TIMELINE_YEAR.start,
    end: defaultYearRange?.at(-1) || DEFAULT_TIMELINE_YEAR.end,
  });
  const [playConfigs, setPlayConfigs] = useState<{
    mode: string;
    speed: number;
    isPlaying: boolean;
    loopCount: number;
  }>({
    mode: PLAYBACK_MODE.ONCE,
    speed: 3,
    isPlaying: false,
    loopCount: 0,
  });

  // console.log("???", year, anchorYear, timeLineYear, playConfigs);
  // console.log("???", playConfigs, anchorYear);

  const canvasRef = useRef(null!);

  // use ref for interval instead of a global variable
  const intervalRef = useRef<ReturnType<typeof setInterval>>(null!);

  // debounceSetStartYear
  // const debounceSetStartTimeLineYear = useCallback(
  //   debounce((newYear) => {
  //     if (newYear < timeLineYear.start || newYear > timeLineYear.end - 1) {
  //       setYear((year) => ({
  //         ...year,
  //         start: timeLineYear.start,
  //       }));
  //     } else if (newYear > anchorYear) {
  //       setYear((year) => ({
  //         ...year,
  //         start: newYear,
  //       }));
  //       setAnchorYear(newYear);
  //     }
  //   }, 1000),
  //   [anchorYear, timeLineYear],
  // );
  const debounceSetStartTimeLineYear = useMemo(
    () =>
      debounce((newYear) => {
        if (newYear < timeLineYear.start || newYear > timeLineYear.end - 1) {
          setYear((year) => ({
            ...year,
            start: timeLineYear.start,
          }));
        } else if (newYear > anchorYear) {
          setYear((year) => ({
            ...year,
            start: newYear,
          }));
          setAnchorYear(newYear);
        }
      }, 1000),
    [anchorYear, timeLineYear],
  );

  // debounceSetEndYear
  const debounceSetTimeLineEndYear = useMemo(
    () =>
      debounce((newYear) => {
        if (newYear < timeLineYear.start || newYear > timeLineYear.end - 1) {
          setYear((prevYear) => ({
            ...prevYear,
            end: timeLineYear.end,
          }));
        } else if (newYear < anchorYear) {
          setYear((prevYear) => ({
            ...prevYear,
            end: newYear,
          }));
          setAnchorYear(year.start);
        }
      }, 1000),
    [year, anchorYear, timeLineYear],
  );

  // cleanup so debounce doesn't leak
  useEffect(() => {
    return () => {
      debounceSetStartTimeLineYear.cancel();
      debounceSetTimeLineEndYear.cancel();
    };
  }, [debounceSetStartTimeLineYear, debounceSetTimeLineEndYear]);

  const handleResetYear = useCallback(() => {
    const defaultValue = {
      start: defaultYearRange?.at(0) || DEFAULT_TIMELINE_YEAR.start,
      end: defaultYearRange?.at(-1) || DEFAULT_TIMELINE_YEAR.end,
    };
    setYear(defaultValue);
    setAnchorYear(defaultValue.start);
    setPlayConfigs((configs) => ({ ...configs, isPlaying: false }));
    setTimeLineYear(defaultValue);
  }, [defaultYearRange]);

  const handleChangeSpeed = useCallback((value: string) => {
    setPlayConfigs((configs) => ({ ...configs, speed: parseFloat(value) }));
  }, []);

  // memoize time line configs drawTimeLine
  const timeLineCongfigs = useMemo(
    () => ({
      years: [],
      margin: { top: 10, left: 5, bottom: 5, right: 20 },
      canvasRef,
      endYear: year.end,
      startYear: year.start,
      onBarDrag: (year: number) => setAnchorYear(year),
      anchorYear,
      onMouseOnBar: () => {
        setPlayConfigs((configs) => ({
          ...configs,
          isPlaying: false,
        }));
      },
      onMouseOutBar: () => {},
      onEndYearChange: (newYear: number) => {
        setYear((year) => ({ ...year, end: newYear }));
      },
      timeLineEndYear: timeLineYear.end,
      onStartYearChange: (newYear: number) => {
        setYear((year) => ({ ...year, start: newYear }));
      },
      timeLineStartYear: timeLineYear.start,
    }),
    [year, anchorYear, timeLineYear],
  );

  // draw timeline
  useEffect(() => {
    drawTimeLine(timeLineCongfigs);
  }, [year, anchorYear, timeLineYear, timeLineCongfigs]);

  // interval to play
  useEffect(() => {
    const { mode, speed, isPlaying } = playConfigs;
    // console.log("*******", playConfigs, anchorYear);
    if (isPlaying) {
      clearInterval(intervalRef.current);
      const intervalTime = INTERVAL1X / Number(speed);

      intervalRef.current = setInterval(() => {
        // once mode
        if (mode === PLAYBACK_MODE.ONCE) {
          setAnchorYear((prevYear) => {
            const nextYear = Number(prevYear) + DURATION_YEARS;

            // rolling end
            if (nextYear > Number(year.end)) {
              setPlayConfigs((configs) => ({
                ...configs,
                isPlaying: false,
              }));

              return prevYear;
            }

            if (restartTrigger) {
              restartTrigger();
            }

            // keep rolling
            return nextYear;
          });
        } else {
          // repeat mode
          setAnchorYear((prevYear) => {
            const nextYear = Number(prevYear) + DURATION_YEARS;

            if (restartTrigger) {
              restartTrigger();
            }
            return nextYear > Number(year.end) ? year.start : nextYear;
          });
        }
      }, intervalTime);
    } else {
      clearInterval(intervalRef.current);
    }

    return () => clearInterval(intervalRef.current);
  }, [year, playConfigs, restartTrigger]);

  return (
    <div className="grid grid-row-2">
      <div className="row-span-1 p-2">
        <div className="grid grid-cols-12 gap-4">
          <div className="col-span-2 flex items-center">
            <YearInput
              min={defaultYearRange?.at(0) || DEFAULT_TIMELINE_YEAR.start}
              max={(defaultYearRange?.at(-1) || DEFAULT_TIMELINE_YEAR.end) - 1}
              type="number"
              name="start"
              label="開始時間"
              value={year.start}
              required
              onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                const value = event.currentTarget.value;
                setYear((year) => ({
                  ...year,
                  start: (value && parseInt(value, 10)) || 0,
                }));
                if (value) {
                  debounceSetStartTimeLineYear(parseInt(value, 10));
                }
              }}
              className="w-full"
              placeholder="開始時間"
            />
          </div>
          <div className="col-span-2 flex items-center">
            <YearInput
              min={(defaultYearRange?.at(0) || DEFAULT_TIMELINE_YEAR.start) + 1}
              max={defaultYearRange?.at(-1) || DEFAULT_TIMELINE_YEAR.end}
              type="number"
              name="end"
              label="結束時間"
              value={year.end}
              required
              onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                const value = event.currentTarget.value;
                setYear((year) => ({
                  ...year,
                  end: (value && parseInt(value, 10)) || 0,
                }));
                if (value) {
                  debounceSetTimeLineEndYear(parseInt(value, 10));
                }
              }}
              className="w-full"
              placeholder="結束時間"
            />
          </div>
          <div className="col-span-2 flex items-end">
            <Button className="cursor-pointer" onClick={handleResetYear}>
              重新設定
            </Button>
          </div>
          <div className="col-span-6 flex justify-end items-end">
            <Select
              name="speed"
              value={`${playConfigs.speed}`}
              onValueChange={handleChangeSpeed}
            >
              <SelectTrigger className="w-1/3">
                <SelectValue placeholder="播放速度" />
              </SelectTrigger>
              <SelectContent>
                {SPEED_OPTIONS.map(({ label, value }) => (
                  <SelectItem key={`speed-${value}`} value={`${value}`}>
                    {label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>
      <div className="row-span-1 p-2">
        <div className="grid grid-cols-12 gap-2">
          <div className="col-span-11 flex items-center h-[100px]">
            <div ref={canvasRef} id="canvas" className="w-full h-full" />
          </div>
          <div className="col-span-1 flex justify-around items-center w-full">
            <Button
              size="icon"
              variant="ghost"
              className="cursor-pointer size-8"
              onClick={() => {
                if (anchorYear >= year.end) {
                  setAnchorYear(year.start);
                }
                setPlayConfigs((configs) => ({
                  ...configs,
                  isPlaying: !configs.isPlaying,
                  // loopCount:
                  //   configs.loopCount === 0 && year.end === anchorYear
                  //     ? 1
                  //     : configs.loopCount,
                }));
              }}
            >
              {playConfigs.isPlaying ? (
                <CirclePauseIcon className="size-6" />
              ) : (
                <CirclePlayIcon className="size-6" />
              )}
            </Button>

            <Button
              size="icon"
              variant="ghost"
              className="cursor-pointer size-8 ml-2"
              onClick={() => {
                setPlayConfigs((configs) => ({
                  ...configs,
                  ...(configs.mode === PLAYBACK_MODE.ONCE && {
                    mode: PLAYBACK_MODE.REPEAT,
                    loopCount: 1,
                  }),
                  ...(configs.mode === PLAYBACK_MODE.REPEAT && {
                    mode: PLAYBACK_MODE.ONCE,
                    loopCount: 0,
                  }),
                }));
              }}
            >
              {playConfigs.mode === PLAYBACK_MODE.ONCE ? (
                <Repeat1Icon className="size-6" />
              ) : (
                <RepeatIcon className="size-6" />
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TimeLine;
