'use client'

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { 
  MapPin, 
  Layers, 
  Clock, 
  Ruler,
  Eye,
  Navigation
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface MapStatsPanelProps {
  map: any | null;
  timelineValues: {
    startYear: number;
    endYear: number;
    currentYear: number;
  };
  enabledLayers: string[];
  className?: string;
}

export function MapStatsPanel({ 
  map, 
  timelineValues, 
  enabledLayers, 
  className 
}: MapStatsPanelProps) {
  // 獲取地圖中心點和縮放級別
  const getMapInfo = () => {
    if (!map) return { center: null, zoom: null };
    
    const center = map.getCenter();
    const zoom = map.getZoom();
    
    return {
      center: {
        lat: center.lat.toFixed(6),
        lng: center.lng.toFixed(6)
      },
      zoom: zoom.toFixed(1)
    };
  };

  const mapInfo = getMapInfo();

  const stats = [
    {
      icon: Navigation,
      label: '地圖中心',
      value: mapInfo.center 
        ? `${mapInfo.center.lat}, ${mapInfo.center.lng}`
        : '載入中...',
      color: 'text-blue-600'
    },
    {
      icon: Eye,
      label: '縮放級別',
      value: mapInfo.zoom ? `Level ${mapInfo.zoom}` : '載入中...',
      color: 'text-green-600'
    },
    {
      icon: Layers,
      label: '啟用圖層',
      value: `${enabledLayers.length} 個圖層`,
      color: 'text-purple-600'
    },
    {
      icon: Clock,
      label: '時間範圍',
      value: `${timelineValues.startYear} - ${timelineValues.endYear}`,
      color: 'text-orange-600'
    },
    {
      icon: MapPin,
      label: '當前年份',
      value: timelineValues.currentYear.toString(),
      color: 'text-red-600'
    }
  ];

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-3">
        <CardTitle className="text-base flex items-center">
          <Ruler className="h-4 w-4 mr-2" />
          地圖資訊
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <div key={index} className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Icon className={cn("h-4 w-4", stat.color)} />
                  <span className="text-sm font-medium text-muted-foreground">
                    {stat.label}
                  </span>
                </div>
                <div className="text-sm font-mono bg-muted/50 p-2 rounded text-center">
                  {stat.value}
                </div>
              </div>
            );
          })}
        </div>
        
        <Separator className="my-4" />
        
        {/* 快速操作提示 */}
        <div className="space-y-2">
          <div className="text-sm font-medium text-muted-foreground">快速操作</div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs text-muted-foreground">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-primary rounded-full"></div>
              <span>拖拽時間軸調整年份範圍</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>點擊工具欄使用測量功能</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span>使用圖層面板控制顯示</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
              <span>滾輪縮放，拖拽移動地圖</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
