// utils/points.ts
import proj4 from 'proj4';

proj4.defs('EPSG:3826', '+proj=tmerc +lat_0=0 +lon_0=121 +k=0.9999 +x_0=250000 +y_0=0 +ellps=GRS80 +units=m +no_defs');

type ApiItem = {
  id: string;
  landName?: string;
  landSerialNumber?: string;
  lat?: string | number;
  long?: string | number;   // 注意鍵名是 long
  x?: string | number;      // TWD97 X
  y?: string | number;      // TWD97 Y
};

export type NormalizedPoint = {
  id: string;
  name: string;
  lat: number;
  lng: number;
};

function parseNum(v: unknown): number | null {
  const n = typeof v === 'string' ? parseFloat(v) : typeof v === 'number' ? v : NaN;
  return Number.isFinite(n) ? n : null;
}

export function normalizePoints(items: ApiItem[]): NormalizedPoint[] {
  const out: NormalizedPoint[] = [];

  for (const it of items) {
    // 1) 先走 lat/long
    const lat = parseNum(it.lat);
    const lng = parseNum((it as any).long); // API 鍵是 long
    if (lat !== null && lng !== null) {
      out.push({
        id: it.id,
        name: it.landName ?? it.landSerialNumber ?? it.id,
        lat,
        lng,
      });
      continue;
    }

    // 2) 再走 TWD97 TM2(121E) x/y -> WGS84
    const x = parseNum(it.x);
    const y = parseNum(it.y);
    if (x !== null && y !== null) {
      const [lng2, lat2] = proj4('EPSG:3826', 'WGS84', [x, y]);
      if (Number.isFinite(lat2) && Number.isFinite(lng2)) {
        out.push({
          id: it.id,
          name: it.landName ?? it.landSerialNumber ?? it.id,
          lat: lat2,
          lng: lng2,
        });
      }
    }
  }
  return out;
}
