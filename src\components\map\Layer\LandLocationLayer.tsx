'use client';

import { useEffect, useState } from 'react';
import { useMap } from 'react-leaflet';
import { LandLocationData } from '@/lib/feature-data';

interface LandLocationLayerProps {
	data: LandLocationData[];
	visible?: boolean;
}

export function LandLocationLayer({ data, visible = true }: LandLocationLayerProps) {
	const map = useMap();
	const [currentZoom, setCurrentZoom] = useState(10);

	useEffect(() => {
		if (!map) return;

		// 設置初始縮放值
		setCurrentZoom(map.getZoom());

		// 監聽縮放變化
		const handleZoomEnd = () => {
			const newZoom = map.getZoom();
			setCurrentZoom(newZoom);
		};

		map.on('zoomend', handleZoomEnd);

		return () => {
			map.off('zoomend', handleZoomEnd);
		};
	}, [map]);

	useEffect(() => {
		if (!map) return;

		let landLayerGroup: any = null;
		let isMounted = true;

		const loadLeaflet = async () => {
			const L = (await import('leaflet')).default;

			// 檢查組件是否仍然掛載
			if (!isMounted) return;

			landLayerGroup = L.layerGroup();

			// 只有當可見且有數據時才創建標記
			if (visible && data.length > 0) {
				if (currentZoom <= 12) {
					// 拉遠時顯示地段 cluster
					const sectionGroups: { [key: string]: LandLocationData[] } = {};

					// 按地段分組
					data.forEach((item) => {
						if (!sectionGroups[item.section]) {
							sectionGroups[item.section] = [];
						}
						sectionGroups[item.section].push(item);
					});

					// 為每個地段創建 cluster 標記
					Object.entries(sectionGroups).forEach(([landName, items]) => {
						if (items.length === 0) return;

						// 計算地段中心點
						const centerLat = items.reduce((sum, item) => sum + item.lat, 0) / items.length;
						const centerLng = items.reduce((sum, item) => sum + item.lng, 0) / items.length;

						// 創建 cluster 圖標
						const clusterIcon = L.divIcon({
							html: `
                <div style="
                  background: rgba(34, 197, 94, 0.9);
                  color: white;
                  padding: 8px;
                  border-radius: 50%;
                  font-size: 14px;
                  font-weight: bold;
                  text-align: center;
                  border: 3px solid white;
                  box-shadow: 0 2px 8px rgba(0,0,0,0.3);
                  min-width: 40px;
                  min-height: 40px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                ">
                  ${items.length}
                </div>
              `,
							className: 'land-cluster-marker',
							iconSize: [40, 40],
							iconAnchor: [20, 20],
						});

						const marker = L.marker([centerLat, centerLng], { icon: clusterIcon });

						marker.bindPopup(`
              <div style="font-size: 14px;">
                <strong>${landName}</strong><br/>
                <strong>地號數量:</strong> ${items.length}<br/>
                <em>放大地圖查看個別地號</em>
              </div>
            `);

						landLayerGroup.addLayer(marker);
					});
				} else {
					// 拉近時顯示個別地號
					data.forEach((item) => {
						const landIcon = L.divIcon({
							html: `
                <div style="
                  background: rgba(16, 185, 129, 0.8);
                  color: white;
                  padding: 4px 8px;
                  border-radius: 4px;
                  font-size: 11px;
                  font-weight: bold;
                  text-align: center;
                  border: 2px solid white;
                  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
                  white-space: nowrap;
                ">
                  ${item.landNumber}
                </div>
              `,
							className: 'land-number-marker',
							iconSize: [60, 20],
							iconAnchor: [30, 10],
						});

						const marker = L.marker([item.lat, item.lng], { icon: landIcon });

						marker.bindPopup(`
              <div style="font-size: 14px;">
                <strong>土地坐落</strong><br/>
                <strong>地號:</strong> ${item.landNumber}<br/>
              </div>
            `);

						landLayerGroup.addLayer(marker);
					});
				}
			}

			// 只有當可見時才添加到地圖
			if (visible && isMounted) {
				landLayerGroup.addTo(map);
			}
		};

		loadLeaflet();

		return () => {
			isMounted = false;
			if (landLayerGroup && map) {
				try {
					map.removeLayer(landLayerGroup);
				} catch (error) {
					// 忽略移除圖層時的錯誤
					console.warn('Error removing land location layer:', error);
				}
			}
		};
	}, [map, data, visible, currentZoom]);

	return null;
}
