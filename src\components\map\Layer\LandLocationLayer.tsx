'use client';

import { useEffect, useState, useMemo } from 'react';
import { useMap } from 'react-leaflet';
import { LandLocationData } from '@/lib/feature-data';
import { filterDataByBounds, sampleDataByZoom, debounce, type MapBounds } from '@/lib/map-performance';

interface LandLocationLayerProps {
	data: LandLocationData[];
	visible?: boolean;
}

export function LandLocationLayer({ data, visible = true }: LandLocationLayerProps) {
	const map = useMap();
	const [currentZoom, setCurrentZoom] = useState(10);
	const [mapBounds, setMapBounds] = useState<MapBounds | null>(null);

	// 使用 useMemo 優化數據處理
	const optimizedData = useMemo(() => {
		if (!data.length || !mapBounds) return data;

		// 1. 先根據視窗範圍過濾
		const boundsFiltered = filterDataByBounds(data, mapBounds, 0.2);

		// 2. 根據縮放層級進行採樣
		const maxPoints = currentZoom <= 12 ? 100 : currentZoom <= 15 ? 500 : 1000;
		const sampled = sampleDataByZoom(boundsFiltered, currentZoom, maxPoints);

		return sampled;
	}, [data, mapBounds, currentZoom]);

	// 防抖的邊界更新函數
	const debouncedUpdateBounds = useMemo(
		() =>
			debounce((bounds: MapBounds) => {
				setMapBounds(bounds);
			}, 300),
		[]
	);

	// 監聽縮放和移動變化
	useEffect(() => {
		if (!map) return;

		// 設置初始值
		setCurrentZoom(map.getZoom());
		const bounds = map.getBounds();
		setMapBounds({
			north: bounds.getNorth(),
			south: bounds.getSouth(),
			east: bounds.getEast(),
			west: bounds.getWest(),
		});

		// 監聽縮放變化
		const handleZoomEnd = () => {
			const newZoom = map.getZoom();
			setCurrentZoom(newZoom);

			const bounds = map.getBounds();
			debouncedUpdateBounds({
				north: bounds.getNorth(),
				south: bounds.getSouth(),
				east: bounds.getEast(),
				west: bounds.getWest(),
			});
		};

		// 監聽移動變化
		const handleMoveEnd = () => {
			const bounds = map.getBounds();
			debouncedUpdateBounds({
				north: bounds.getNorth(),
				south: bounds.getSouth(),
				east: bounds.getEast(),
				west: bounds.getWest(),
			});
		};

		map.on('zoomend', handleZoomEnd);
		map.on('moveend', handleMoveEnd);

		return () => {
			map.off('zoomend', handleZoomEnd);
			map.off('moveend', handleMoveEnd);
		};
	}, [map, debouncedUpdateBounds]);

	useEffect(() => {
		if (!map) return;

		let landLayerGroup: any = null;
		let isMounted = true;

		const loadLeaflet = async () => {
			const L = (await import('leaflet')).default;

			// 檢查組件是否仍然掛載
			if (!isMounted) return;

			landLayerGroup = L.layerGroup();

			// 只有當可見且有數據時才創建標記
			if (visible && optimizedData.length > 0) {
				// 性能監控：只在開發環境下輸出
				if (process.env.NODE_ENV === 'development') {
					console.warn(`LandLocationLayer: 渲染 ${optimizedData.length}/${data.length} 個標記 (縮放: ${currentZoom})`);
				}

				if (currentZoom <= 12) {
					// 拉遠時顯示地段 cluster
					const sectionGroups: { [key: string]: LandLocationData[] } = {};

					// 按地段分組
					optimizedData.forEach((item) => {
						if (!sectionGroups[item.section]) {
							sectionGroups[item.section] = [];
						}
						sectionGroups[item.section].push(item);
					});

					// 為每個地段創建 cluster 標記
					Object.entries(sectionGroups).forEach(([landName, items]) => {
						if (items.length === 0) return;

						// 計算地段中心點
						const centerLat = items.reduce((sum, item) => sum + item.lat, 0) / items.length;
						const centerLng = items.reduce((sum, item) => sum + item.lng, 0) / items.length;

						// 創建 cluster 圖標
						const clusterIcon = L.divIcon({
							html: `
                <div style="
                  background: rgba(34, 197, 94, 0.9);
                  color: white;
                  padding: 8px;
                  border-radius: 50%;
                  font-size: 14px;
                  font-weight: bold;
                  text-align: center;
                  border: 3px solid white;
                  box-shadow: 0 2px 8px rgba(0,0,0,0.3);
                  min-width: 40px;
                  min-height: 40px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                ">
                  ${items.length}
                </div>
              `,
							className: 'land-cluster-marker',
							iconSize: [40, 40],
							iconAnchor: [20, 20],
						});

						const marker = L.marker([centerLat, centerLng], { icon: clusterIcon });

						marker.bindPopup(`
              <div style="font-size: 14px;">
                <strong>${landName}</strong><br/>
                <strong>地號數量:</strong> ${items.length}<br/>
                <em>放大地圖查看個別地號</em>
              </div>
            `);

						landLayerGroup.addLayer(marker);
					});
				} else {
					// 拉近時顯示個別地號
					optimizedData.forEach((item) => {
						const landIcon = L.divIcon({
							html: `
                <div style="
                  background: rgba(16, 185, 129, 0.8);
                  color: white;
                  padding: 4px 8px;
                  border-radius: 4px;
                  font-size: 11px;
                  font-weight: bold;
                  text-align: center;
                  border: 2px solid white;
                  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
                  white-space: nowrap;
                ">
                  ${item.landNumber}
                </div>
              `,
							className: 'land-number-marker',
							iconSize: [60, 20],
							iconAnchor: [30, 10],
						});

						const marker = L.marker([item.lat, item.lng], { icon: landIcon });

						marker.bindPopup(`
              <div style="font-size: 14px;">
                <strong>土地坐落</strong><br/>
                <strong>地號:</strong> ${item.landNumber}<br/>
              </div>
            `);

						landLayerGroup.addLayer(marker);
					});
				}
			}

			// 只有當可見時才添加到地圖
			if (visible && isMounted) {
				landLayerGroup.addTo(map);
			}
		};

		loadLeaflet();

		return () => {
			isMounted = false;
			if (landLayerGroup && map) {
				try {
					map.removeLayer(landLayerGroup);
				} catch (error) {
					// 忽略移除圖層時的錯誤
					console.warn('Error removing land location layer:', error);
				}
			}
		};
	}, [map, optimizedData, visible, currentZoom]);

	return null;
}
