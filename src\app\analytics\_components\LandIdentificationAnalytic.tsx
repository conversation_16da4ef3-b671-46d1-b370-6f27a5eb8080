/* eslint-disable */
"use client";
import { useState, useLayoutEffect } from "react";

const LandIdentificationAnalytic = (): React.JSX.Element => {
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // get data
  useLayoutEffect(() => {}, []);

  return (
    <div className="grid grid-row-3 gap-4">
      <div className="row-span-1 p-4">標題</div>
      <div className="row-span-1 p-4">時間條</div>
      <div className="row-span-1 p-4">圖</div>
    </div>
  );
};

export default LandIdentificationAnalytic;
