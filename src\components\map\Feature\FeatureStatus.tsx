'use client'

import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Users, 
  MapPin, 
  Flame, 
  Search,
  Shapes,
  X,
  Eye,
  EyeOff
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { FeatureType } from './FeatureSelector';

interface FeatureStatusProps {
  selectedFeature: FeatureType;
  dataCount: number;
  isVisible?: boolean;
  onToggleVisibility?: () => void;
  onClear: () => void;
  className?: string;
}

const featureConfig = {
  'surname-distribution': {
    name: '姓氏分布',
    icon: Users,
    color: 'text-blue-600',
    bgColor: 'bg-blue-50'
  },
  'land-location': {
    name: '土地坐落',
    icon: MapPin,
    color: 'text-green-600',
    bgColor: 'bg-green-50'
  },
  'transaction-heatmap': {
    name: '土地交易次數',
    icon: Flame,
    color: 'text-red-600',
    bgColor: 'bg-red-50'
  },
  'search-radius': {
    name: '搜索半徑',
    icon: Search,
    color: 'text-purple-600',
    bgColor: 'bg-purple-50'
  },
  'land-type-distribution': {
    name: '各種地目分布',
    icon: Shapes,
    color: 'text-orange-600',
    bgColor: 'bg-orange-50'
  }
};

export function FeatureStatus({
  selectedFeature,
  dataCount,
  isVisible = true,
  onToggleVisibility,
  onClear,
  className
}: FeatureStatusProps) {
  if (!selectedFeature) return null;

  const config = featureConfig[selectedFeature];
  const Icon = config.icon;

  return (
    <Card className={cn("w-fit", "p-0", className)}>
      <CardContent className="p-3">
        <div className="flex items-center space-x-3">
          <div className={cn("p-2 rounded-md", config.bgColor)}>
            <Icon className={cn("h-4 w-4", config.color)} />
          </div>
          
          <div className="flex-1">
            <div className="text-sm font-medium">{config.name}</div>
            <div className="text-xs text-muted-foreground">
              {dataCount} 個數據點
            </div>
          </div>
          
          <div className="flex items-center space-x-1">
            {onToggleVisibility && (
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={onToggleVisibility}
                title={isVisible ? '隱藏' : '顯示'}
              >
                {isVisible ? (
                  <Eye className="h-3 w-3" />
                ) : (
                  <EyeOff className="h-3 w-3" />
                )}
              </Button>
            )}
            
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
              onClick={onClear}
              title="清除"
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
