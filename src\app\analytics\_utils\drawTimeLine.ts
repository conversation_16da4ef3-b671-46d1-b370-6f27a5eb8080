import { type RefObject } from "react";
import * as d3 from "d3";

// Typescript
interface DrawTimeLineProps {
  years: number[];
  margin?: { top: number; left: number; bottom: number; right: number };
  endYear: number;
  startYear: number;
  canvasRef: RefObject<HTMLDivElement>;
  onBarDrag: <T extends number>(item: T) => void;
  anchorYear: number;
  onMouseOnBar: <T>(item: T) => void;
  onMouseOutBar: <T>(item: T) => void;
  onEndYearChange: <T extends number>(item: T) => void;
  timeLineEndYear: number;
  onStartYearChange: <T extends number>(item: T) => void;
  timeLineStartYear: number;
}

const drawTimeLine = ({
  // years,
  margin = { top: 10, left: 5, bottom: 5, right: 20 },
  canvasRef,
  endYear,
  startYear,
  onBarDrag,
  anchorYear,
  onMouseOnBar,
  onMouseOutBar,
  onEndYearChange,
  timeLineEndYear,
  onStartYearChange,
  timeLineStartYear,
}: DrawTimeLineProps) => {
  const canvasWidth =
    canvasRef.current.clientWidth - margin.left - margin.right;
  const canvasHeight =
    canvasRef.current.clientHeight - margin.top - margin.bottom;

  // initialize、clear SVG canvas
  let svgCanvas = d3
    .select(canvasRef.current)
    .select<SVGSVGElement>("svg")
    .style("overflow", "initial");

  if (svgCanvas.node()) {
    svgCanvas.selectAll("*").remove();
  } else {
    svgCanvas = d3
      .select(canvasRef.current)
      .append<SVGSVGElement>("svg")
      .attr("class", "svgCanvas")
      .attr("width", canvasWidth)
      .attr("height", canvasHeight);
  }

  // create scale
  const xScale = d3
    .scaleLinear()
    .domain([timeLineStartYear, timeLineEndYear])
    .range([0, canvasWidth]);

  // create axis
  const xAxis = d3
    .axisBottom(xScale)
    .tickFormat(d3.format("d"))
    .ticks((timeLineEndYear - timeLineStartYear) / 20)
    .tickSize(0)
    .tickPadding(15);

  // draw axis
  const axisG = svgCanvas
    .append("g")
    .attr("class", "axis")
    .attr(
      "transform",
      `translate(${margin.left}, ${margin.top + canvasHeight / 2})`,
    )
    .style("font-size", "12px")
    .call(xAxis);

  // style axis
  axisG
    .selectAll("path, line")
    .attr("stroke", "#f3f3f3")
    .attr("stroke-width", 4);
  axisG.selectAll("text").attr("fill", "#000000");

  // drag handler
  const createAnchorTooltip = () => {
    const g = svgCanvas.append("g").attr("class", "anchor-tooltip");

    // main box
    g.append("rect")
      // .attr("class", "anchor-year-box")
      .attr("x", configs.anchorX - 22)
      .attr("y", configs.anchorY - configs.boxHeight / 2 - 15)
      .attr("width", 45)
      .attr("height", 22.5)
      .attr("rx", configs.cornerRadius)
      .attr("ry", configs.cornerRadius)
      .attr("fill", "#104860")
      .attr("stroke", "#104860");

    // pointer box
    g.append("rect")
      // .attr("class", "anchor-year-sub-box")
      .attr("x", configs.anchorX - 3)
      .attr("y", configs.anchorY - configs.boxHeight / 2 + 5)
      .attr("width", 5)
      .attr("height", 5)
      .attr("rx", 5)
      .attr("ry", 5)
      .attr("fill", "#104860")
      .attr("stroke", "#104860");

    // year text
    g.append("text")
      // .attr("class", "anchor-year-text")
      .attr("x", configs.anchorX)
      .attr("y", configs.anchorY - configs.boxHeight / 2)
      .attr("text-anchor", "middle")
      .attr("font-size", "12px")
      .attr("fill", "#ffffff")
      .text(anchorYear);
  };

  const removeAnchorTooltip = () => {
    svgCanvas.selectAll(".anchor-tooltip").remove();
  };

  const createDragHandler = ({
    callback,
    yearType,
    checkValidTimeRange,
  }: {
    callback: <T extends number>(item: T) => void;
    yearType: string;
    checkValidTimeRange: (newYear: number) => boolean;
  }) =>
    d3
      .drag<SVGRectElement, unknown, unknown>()
      .on("start", function () {
        d3.select(this).raise().attr("fill", "#384B70");
        createAnchorTooltip();
      })
      .on("drag", function (event) {
        const newX = event.x - margin.left;
        const newYear = Math.round(xScale.invert(newX));

        if (checkValidTimeRange(newYear)) {
          d3.select(this).attr("x", xScale(newYear - 4));

          // special case for anchor dragging
          if (typeof callback === "function") {
            if (yearType === "anchor") {
              d3.select(".year-range-background")
                .attr("x", xScale(startYear))
                .attr("width", xScale(newYear) - xScale(startYear));
            }

            callback(newYear);
          }

          if (
            (yearType === "start" && newYear >= anchorYear) ||
            (yearType === "end" && newYear <= anchorYear)
          ) {
            d3.select(".anchor-bar").attr("x", xScale(newYear) - 4);
            onBarDrag(newYear);
          }
        }
      })
      .on("end", function () {
        if (yearType !== "anchor") {
          d3.select(this).attr("fill", "#104860");
        }

        removeAnchorTooltip();
      });

  // define drag handlers
  const dragHandlers = {
    start: createDragHandler({
      callback: onStartYearChange,
      yearType: "start",
      checkValidTimeRange: (newYear) =>
        newYear >= timeLineStartYear && newYear < endYear,
    }),
    end: createDragHandler({
      callback: onEndYearChange,
      yearType: "end",
      checkValidTimeRange: (newYear) =>
        newYear <= timeLineEndYear && newYear > startYear,
    }),
    anchor: createDragHandler({
      callback: onBarDrag,
      yearType: "anchor",
      checkValidTimeRange: (newYear) =>
        newYear >= startYear && newYear <= endYear,
    }),
  };

  // configs
  const configs = {
    barWidth: 8,
    barHeight: 8,
    boxWidth: 40,
    boxHeight: 40,
    anchorX: xScale(anchorYear) + margin.left,
    anchorY: margin.top + canvasHeight / 2,
    cornerRadius: 10,
  };

  // draw time line background
  svgCanvas
    .append("rect")
    .attr("class", "year-range-background-bottom")
    .attr("x", xScale(startYear))
    .attr("y", -configs.barHeight / 3)
    .attr(
      "transform",
      `translate(${margin.left}, ${margin.top + canvasHeight / 2})`,
    )
    .attr("width", xScale(endYear) - xScale(startYear))
    .attr("height", 5)
    .attr("fill", "#BDBDBD")
    .attr("rx", 5);

  // draw filled time line (to anchor)
  if (anchorYear) {
    svgCanvas
      .append("rect")
      .attr("class", "year-range-background")
      .attr("x", xScale(startYear))
      .attr("y", -configs.barHeight / 3)
      .attr(
        "transform",
        `translate(${margin.left}, ${margin.top + canvasHeight / 2})`,
      )
      .attr("width", xScale(anchorYear) - xScale(startYear))
      .attr("height", 5)
      .attr("fill", "#104860")
      .attr("rx", 5);

    svgCanvas
      .append("rect")
      .attr("class", "anchor-bar")
      .attr("x", xScale(anchorYear) - 4)
      .attr("y", -configs.barHeight / 2)
      .attr(
        "transform",
        `translate(${margin.left}, ${margin.top + canvasHeight / 2})`,
      )
      .attr("width", configs.barWidth)
      .attr("height", configs.barHeight)
      .attr("fill", "#104860")
      .attr("rx", 5)
      .style("cursor", "grab")
      .call(dragHandlers.anchor)
      .on("drag", function () {
        createAnchorTooltip();
      })
      .on("mouseover", function (event) {
        d3.select(this).attr("fill", "#384B70");
        // createAnchorTooltip();
        if (typeof onMouseOnBar === "function") {
          onMouseOnBar(event);
        }
      })
      .on("mouseout", function (event) {
        // removeAnchorTooltip();
        if (typeof onMouseOutBar === "function") {
          onMouseOutBar(event);
        }
      });

    // show anchor year tooltip
    if (anchorYear > timeLineStartYear && anchorYear < timeLineEndYear) {
      createAnchorTooltip();
    }
  }

  // ------------------------
  // CREATE START & END YEAR MARKERS
  // ------------------------
  const createYearMarker = ({
    year,
    className,
    dragHandler,
  }: {
    year: number;
    className: string;
    dragHandler: d3.DragBehavior<SVGRectElement, unknown, unknown>;
  }) => {
    // create marker point
    svgCanvas
      .append("rect")
      .attr("class", `${className}-bar`)
      .attr("x", xScale(year) - 4)
      .attr("y", -configs.barHeight / 2)
      .attr(
        "transform",
        `translate(${margin.left}, ${margin.top + canvasHeight / 2})`,
      )
      .attr("width", configs.barWidth)
      .attr("height", configs.barHeight)
      .attr("fill", "#104860")
      .attr("rx", 5)
      .style("cursor", "grab")
      .call(dragHandler);

    // create the year display box ( if not an anchor point)
    if (className !== "anchor") {
      // main box
      svgCanvas
        .append("rect")
        .attr("class", `${className}-box`)
        .attr("x", xScale(year) + configs.boxWidth / 2 - 37)
        .attr("y", configs.anchorY - configs.boxHeight / 2 - 15)
        .attr("width", 45)
        .attr("height", 22.5)
        .attr("rx", configs.cornerRadius)
        .attr("ry", configs.cornerRadius)
        .attr("fill", "#104860")
        .attr("stroke", "#104860");

      // pointer box
      svgCanvas
        .append("rect")
        .attr("class", `${className}-sub-box`)
        .attr("x", xScale(year) + configs.boxWidth / 2 - 17.5)
        .attr("y", configs.anchorY - configs.boxHeight / 2 + 5)
        .attr("width", 5)
        .attr("height", 5)
        .attr("rx", 5)
        .attr("ry", 5)
        .attr("fill", "#104860")
        .attr("stroke", "#104860");

      // year text
      svgCanvas
        .append("text")
        .attr("class", `${className}-text`)
        .attr("x", xScale(year) + configs.boxWidth / 2 - 14.5)
        .attr("y", configs.anchorY - configs.boxHeight / 2)
        .attr("text-anchor", "middle")
        .attr("font-size", "12px")
        .attr("fill", "#ffffff")
        .text(year);
    }
  };

  // create anchor year if years array has data
  // if (years.length) {
  //   const createAnchorTooltip = () => {
  //     // main box
  //     svgCanvas
  //       .append("rect")
  //       .attr("class", "anchor-year-box")
  //       .attr("x", configs.anchorX - 22)
  //       .attr("y", configs.anchorY - configs.boxHeight / 2 - 15)
  //       .attr("width", 45)
  //       .attr("height", 22.5)
  //       .attr("rx", configs.cornerRadius)
  //       .attr("ry", configs.cornerRadius)
  //       .attr("fill", "#104860")
  //       .attr("stroke", "#104860");

  //     // pointer box
  //     svgCanvas
  //       .append("rect")
  //       .attr("class", "anchor-year-sub-box")
  //       .attr("x", configs.anchorX - 3)
  //       .attr("y", configs.anchorY - configs.boxHeight / 2 + 5)
  //       .attr("width", 5)
  //       .attr("height", 5)
  //       .attr("rx", 5)
  //       .attr("ry", 5)
  //       .attr("fill", "#104860")
  //       .attr("stroke", "#104860");

  //     // year text
  //     svgCanvas
  //       .append("text")
  //       .attr("class", "anchor-year-text")
  //       .attr("x", configs.anchorX)
  //       .attr("y", configs.anchorY - configs.boxHeight / 2)
  //       .attr("text-anchor", "middle")
  //       .attr("font-size", "12px")
  //       .attr("fill", "#ffffff")
  //       .text(anchorYear);
  //   };

  //   const removeAnchorTooltip = () => {
  //     svgCanvas.selectAll(".anchor-year").remove();
  //     svgCanvas.selectAll(".anchor-year-box").remove();
  //     svgCanvas.selectAll(".anchor-year-sub-box").remove();
  //     svgCanvas.selectAll(".anchor-year-text").remove();
  //   };

  //   svgCanvas
  //     .append("rect")
  //     .attr("class", "anchor-bar")
  //     .attr("x", xScale(anchorYear) - 4)
  //     .attr("y", -configs.barHeight / 2)
  //     .attr(
  //       "transform",
  //       `translate(${margin.left}, ${margin.top + canvasHeight / 2})`,
  //     )
  //     .attr("width", configs.barWidth)
  //     .attr("height", configs.barHeight)
  //     .attr("fill", "#104860")
  //     .attr("rx", 5)
  //     .style("cursor", "ew-resize")
  //     .call(dragHandlers.anchor)
  //     .on("mouseover", function (event) {
  //       d3.select(this).attr("fill", "#384B70");
  //       createAnchorTooltip();
  //       if (typeof onMouseOnBar === "function") {
  //         onMouseOnBar(event);
  //       }
  //     })
  //     .on("mouseout", function (event) {
  //       removeAnchorTooltip();
  //       if (typeof onMouseOutBar === "function") {
  //         onMouseOutBar(event);
  //       }
  //     });
  // }

  // create the year markers, make sure the startYear and endYear markers are created after the anchor bar
  createYearMarker({
    year: startYear,
    className: "start-year",
    dragHandler: dragHandlers.start,
  });
  createYearMarker({
    year: endYear,
    className: "end-year",
    dragHandler: dragHandlers.end,
  });
};

export default drawTimeLine;
