# 地圖配置說明

## 概述

本專案使用 Leaflet 作為地圖引擎，所有地圖相關的配置都集中在 `map.config.ts` 文件中，便於維護和自定義。

## 配置文件結構

### 1. 基本地圖配置 (`MapConfig`)

```typescript
export const mapConfig: MapConfig = {
  defaultCenter: [25.0330, 121.5654], // 預設中心點（台北市政府）
  defaultZoom: 10,                    // 預設縮放級別
  minZoom: 6,                         // 最小縮放級別
  maxZoom: 18,                        // 最大縮放級別
  tileLayers: [...],                  // 底圖圖層配置
  overlayLayers: [...],               // 疊加圖層配置
  mapOptions: {...}                   // 地圖選項
}
```

### 2. 底圖圖層配置 (`TileLayerConfig`)

目前提供四種底圖：
- **OpenStreetMap**: 開源地圖，預設選項
- **衛星影像**: Esri 衛星影像
- **地形圖**: OpenTopoMap 地形圖
- **CartoDB 淺色**: 簡潔的淺色底圖

### 3. 疊加圖層配置 (`OverlayLayerConfig`)

支援的圖層類型：
- `geojson`: GeoJSON 格式的向量圖層
- `wms`: WMS 服務圖層
- `marker`: 標記點圖層

預設疊加圖層：
- 土地使用分區
- 道路網絡
- 建築物
- 地形等高線

## 自定義配置

### 修改預設中心點和縮放級別

```typescript
// 修改為其他城市，例如高雄市
defaultCenter: [22.6273, 120.3014],
defaultZoom: 12,
```

### 添加新的底圖圖層

```typescript
{
  id: 'custom_map',
  name: '自定義地圖',
  url: 'https://your-tile-server.com/{z}/{x}/{y}.png',
  attribution: '&copy; Your Attribution',
  maxZoom: 18
}
```

### 添加新的疊加圖層

```typescript
{
  id: 'new_layer',
  name: '新圖層',
  type: 'geojson',
  isVisible: true,
  style: {
    color: '#ff0000',
    weight: 2,
    opacity: 0.8,
    fillOpacity: 0.3
  }
}
```

### 修改地圖工具配置

```typescript
export const mapTools = {
  measurement: {
    distance: {
      enabled: true,
      color: '#ff0000',    // 距離測量線條顏色
      weight: 3            // 線條粗細
    },
    area: {
      enabled: true,
      color: '#00ff00',    // 面積測量邊框顏色
      weight: 2,
      fillOpacity: 0.2     // 填充透明度
    }
  }
}
```

## 使用方式

### 在組件中使用地圖

```typescript
import LeafletMap from '@/components/map/LeafletMap';
import { mapConfig } from '@/config/map.config';

function MyMapComponent() {
  const [map, setMap] = useState(null);
  
  const handleMapReady = (mapInstance) => {
    setMap(mapInstance);
    // 地圖準備就緒後的操作
  };

  return (
    <LeafletMap 
      height="400px"
      onMapReady={handleMapReady}
      enabledLayers={['land_use', 'roads']}
    />
  );
}
```

### 動態控制圖層顯示

```typescript
const [enabledLayers, setEnabledLayers] = useState(['land_use']);

const toggleLayer = (layerId) => {
  setEnabledLayers(prev => 
    prev.includes(layerId) 
      ? prev.filter(id => id !== layerId)
      : [...prev, layerId]
  );
};
```

## 注意事項

1. **SSR 兼容性**: 所有地圖相關組件都使用動態導入以避免服務端渲染問題
2. **圖標修復**: 已修復 Leaflet 預設圖標在 Next.js 中的顯示問題
3. **樣式自定義**: 可在 `globals.css` 中自定義 Leaflet 控制項樣式
4. **性能考量**: 大量圖層數據建議使用分層載入或虛擬化技術

## 擴展功能

### 添加 WMS 圖層支援

```typescript
// 在 LeafletMap 組件中添加 WMS 圖層渲染邏輯
if (layer.type === 'wms') {
  return (
    <WMSTileLayer
      url={layer.url}
      layers={layer.layers}
      format="image/png"
      transparent={true}
    />
  );
}
```

### 集成地理編碼服務

```typescript
// 添加地址搜尋功能
const geocodeAddress = async (address) => {
  const response = await fetch(`https://api.geocoding-service.com/search?q=${address}`);
  const data = await response.json();
  return data.results[0];
};
```

## 故障排除

### 地圖不顯示
- 檢查網路連接
- 確認底圖 URL 是否正確
- 檢查瀏覽器控制台是否有錯誤

### 圖層不顯示
- 確認圖層數據格式正確
- 檢查圖層樣式配置
- 驗證圖層 URL 是否可訪問

### 性能問題
- 減少同時顯示的圖層數量
- 使用適當的縮放級別限制
- 考慮使用圖層聚合技術
