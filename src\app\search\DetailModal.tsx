'use client'

import { MapPin, Filter, DollarSign, Calendar, Search } from "lucide-react";

interface DetailPanelProps {
  data: any; // 這裡用 any，方便接收你原本 results 的資料結構
  onClose: () => void;
}

export default function DetailPanel({ data, onClose }: DetailPanelProps) {
  if (!data) return null;

  return (
    <div className="bg-white rounded-lg shadow-lg border border-gray-200 mt-8">
      {/* 標題欄 */}
      <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-blue-50">
        <h2 className="text-2xl font-bold text-gray-900">土地詳細資訊</h2>
        <button
          onClick={onClose}
          className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors flex items-center"
        >
          <Search className="w-4 h-4 mr-2" />
          回到搜尋
        </button>
      </div>

      {/* 內容區域 */}
      <div className="p-6 space-y-6">
        {/* 基本資訊 */}
        <div className="bg-blue-50 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-blue-900 mb-3 flex items-center">
            <MapPin className="w-5 h-5 mr-2" />
            基本資訊
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-600">地段</label>
              <p className="text-gray-900 mt-1">{data.landName || '-'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-600">編號</label>
              <p className="text-gray-900 mt-1">#{data.id?.toString().padStart(6,'0')}</p>
            </div>

            {/* 新增欄位 */}
            <div>
              <label className="text-sm font-medium text-gray-600">箱號</label>
              <p className="text-gray-900 mt-1">{data.boxNumber || '-'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-600">原典藏地</label>
              <p className="text-gray-900 mt-1">{data.collectionPlace || '-'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-600">地號</label>
              <p className="text-gray-900 mt-1">{data.landSerialNumber || '-'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-600">典權胎權</label>
              <p className="text-gray-900 mt-1">{data.pawnRight || '-'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-600">贌耕權</label>
              <p className="text-gray-900 mt-1">{data.plowingRight || '-'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-600">現存資料</label>
              <p className="text-gray-900 mt-1">{data.source || '-'}</p>
            </div>
          </div>
        </div>

        {/* 其他資訊 */}
        {/* <div className="bg-yellow-50 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-yellow-900 mb-3">其他資訊</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-600">最後更新</label>
              <p className="text-gray-900 mt-1 flex items-center">
                <Calendar className="w-4 h-4 mr-1" />
                {data.lastUpdate || '-'}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-600">編號</label>
              <p className="text-gray-900 mt-1">#{data.id?.toString().padStart(6,'0')}</p>
            </div>

            {/* 新增欄位 */}
            {/*<div>
              <label className="text-sm font-medium text-gray-600">箱號</label>
              <p className="text-gray-900 mt-1">{data.boxNumber || '-'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-600">原典藏地</label>
              <p className="text-gray-900 mt-1">{data.collectionPlace || '-'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-600">地號</label>
              <p className="text-gray-900 mt-1">{data.landSerialNumber || '-'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-600">典權胎權</label>
              <p className="text-gray-900 mt-1">{data.pawnRight || '-'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-600">贌耕權</label>
              <p className="text-gray-900 mt-1">{data.plowingRight || '-'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-600">現存資料</label>
              <p className="text-gray-900 mt-1">{data.source || '-'}</p>
            </div>
          </div>
        </div> */}
      </div>
      {/* 底部按鈕 */}
      <div className="p-6 border-t border-gray-200 flex justify-end space-x-3 bg-gray-50">
        <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
          加入收藏
        </button>
        <button className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors">
          聯絡業主
        </button>
      </div>
    </div>
  );
}
