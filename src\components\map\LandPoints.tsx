// components/LandPoints.tsx
'use client';

import { useEffect, useMemo } from 'react';
import { Marker, Popup, useMap } from 'react-leaflet';
import L from 'leaflet';
import type { NormalizedPoint } from '@/utils/points';

export default function LandPoints({ points }: { points: NormalizedPoint[] }) {
  const map = useMap();

  const markers = useMemo(
    () =>
      points.map(p => (
        <Marker key={p.id} position={[p.lat, p.lng] as L.LatLngExpression}>
          <Popup>
            <div>
              <div><b>{p.name}</b></div>
              <div>ID：{p.id}</div>
              <div>Lat/Lng：{p.lat.toFixed(6)}, {p.lng.toFixed(6)}</div>
            </div>
          </Popup>
        </Marker>
      )),
    [points]
  );

  useEffect(() => {
    if (!points.length) return;
    const b = L.latLngBounds(points.map(p => [p.lat, p.lng] as [number, number]));
    map.fitBounds(b, { padding: [24, 24] });
  }, [points, map]);

  return <>{markers}</>;
}
