'use client'

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Ruler, Square, RotateCcw, X } from 'lucide-react';
import { cn } from '@/lib/utils';

interface MapMeasurementToolProps {
  map: any | null;
  isActive: boolean;
  onClose: () => void;
  className?: string;
}

export function MapMeasurementTool({ 
  map, 
  isActive, 
  onClose, 
  className 
}: MapMeasurementToolProps) {
  const [measurementType, setMeasurementType] = useState<'distance' | 'area' | null>(null);
  const [measurements, setMeasurements] = useState({
    distance: '',
    area: ''
  });
  const [isDrawing, setIsDrawing] = useState(false);
  const [drawnItems, setDrawnItems] = useState<any[]>([]);

  // 動態導入 Leaflet
  const [L, setL] = useState<any>(null);
  
  useEffect(() => {
    if (typeof window !== 'undefined') {
      import('leaflet').then((leaflet) => {
        setL(leaflet.default);
      });
    }
  }, []);

  // 清除所有測量結果
  const clearMeasurements = () => {
    if (map && drawnItems.length > 0) {
      drawnItems.forEach(item => {
        map.removeLayer(item);
      });
      setDrawnItems([]);
      setMeasurements({ distance: '', area: '' });
      setIsDrawing(false);
      setMeasurementType(null);
    }
  };

  // 計算兩點間距離（使用 Haversine 公式）
  const calculateDistance = (latlng1: any, latlng2: any) => {
    if (!L) return 0;
    return latlng1.distanceTo(latlng2);
  };

  // 計算多邊形面積
  const calculatePolygonArea = (points: any[]) => {
    if (!L || points.length < 3) return 0;
    
    const polygon = L.polygon(points);
    // 使用 Leaflet 的內建面積計算（平方米）
    let area = 0;
    const n = points.length;
    
    for (let i = 0; i < n; i++) {
      const j = (i + 1) % n;
      const lat1 = points[i].lat * Math.PI / 180;
      const lat2 = points[j].lat * Math.PI / 180;
      const lng1 = points[i].lng * Math.PI / 180;
      const lng2 = points[j].lng * Math.PI / 180;
      
      area += (lng2 - lng1) * (2 + Math.sin(lat1) + Math.sin(lat2));
    }
    
    area = Math.abs(area * 6378137 * 6378137 / 2);
    return area;
  };

  // 開始距離測量
  const startDistanceMeasurement = () => {
    if (!map || !L) return;
    
    setMeasurementType('distance');
    setIsDrawing(true);
    
    let points: any[] = [];
    let polyline: any = null;
    let markers: any[] = [];
    
    const onClick = (e: any) => {
      points.push(e.latlng);
      
      // 添加標記
      const marker = L.marker(e.latlng).addTo(map);
      markers.push(marker);
      setDrawnItems(prev => [...prev, marker]);
      
      if (points.length > 1) {
        // 移除舊的線條
        if (polyline) {
          map.removeLayer(polyline);
          setDrawnItems(prev => prev.filter(item => item !== polyline));
        }
        
        // 繪製新的線條
        polyline = L.polyline(points, {
          color: '#ff0000',
          weight: 3
        }).addTo(map);
        
        setDrawnItems(prev => [...prev, polyline]);
        
        // 計算總距離
        let totalDistance = 0;
        for (let i = 1; i < points.length; i++) {
          totalDistance += calculateDistance(points[i-1], points[i]);
        }
        
        setMeasurements(prev => ({
          ...prev,
          distance: `${(totalDistance / 1000).toFixed(2)} 公里`
        }));
      }
    };
    
    map.on('click', onClick);
    
    // 雙擊結束測量
    const onDoubleClick = () => {
      map.off('click', onClick);
      map.off('dblclick', onDoubleClick);
      setIsDrawing(false);
    };
    
    map.on('dblclick', onDoubleClick);
  };

  // 開始面積測量
  const startAreaMeasurement = () => {
    if (!map || !L) return;
    
    setMeasurementType('area');
    setIsDrawing(true);
    
    let points: any[] = [];
    let polygon: any = null;
    let markers: any[] = [];
    
    const onClick = (e: any) => {
      points.push(e.latlng);
      
      // 添加標記
      const marker = L.marker(e.latlng).addTo(map);
      markers.push(marker);
      setDrawnItems(prev => [...prev, marker]);
      
      if (points.length > 2) {
        // 移除舊的多邊形
        if (polygon) {
          map.removeLayer(polygon);
          setDrawnItems(prev => prev.filter(item => item !== polygon));
        }
        
        // 繪製新的多邊形
        polygon = L.polygon(points, {
          color: '#00ff00',
          weight: 2,
          fillOpacity: 0.2
        }).addTo(map);
        
        setDrawnItems(prev => [...prev, polygon]);
        
        // 計算面積
        const area = calculatePolygonArea(points);
        setMeasurements(prev => ({
          ...prev,
          area: `${(area / 10000).toFixed(2)} 公頃`
        }));
      }
    };
    
    map.on('click', onClick);
    
    // 雙擊結束測量
    const onDoubleClick = () => {
      map.off('click', onClick);
      map.off('dblclick', onDoubleClick);
      setIsDrawing(false);
    };
    
    map.on('dblclick', onDoubleClick);
  };

  if (!isActive) return null;

  return (
    <Card className={cn("w-80", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base">測量工具</CardTitle>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
            onClick={onClose}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 工具按鈕 */}
        <div className="flex gap-2">
          <Button
            variant={measurementType === 'distance' ? 'default' : 'outline'}
            size="sm"
            className="flex-1"
            onClick={startDistanceMeasurement}
            disabled={isDrawing && measurementType !== 'distance'}
          >
            <Ruler className="h-4 w-4 mr-2" />
            距離
          </Button>
          <Button
            variant={measurementType === 'area' ? 'default' : 'outline'}
            size="sm"
            className="flex-1"
            onClick={startAreaMeasurement}
            disabled={isDrawing && measurementType !== 'area'}
          >
            <Square className="h-4 w-4 mr-2" />
            面積
          </Button>
        </div>

        <Separator />

        {/* 測量結果 */}
        <div className="space-y-3">
          <div className="text-sm font-medium">測量結果</div>
          
          {measurements.distance && (
            <div className="flex justify-between items-center p-2 bg-muted rounded-md">
              <span className="text-sm text-muted-foreground">距離:</span>
              <span className="font-medium">{measurements.distance}</span>
            </div>
          )}
          
          {measurements.area && (
            <div className="flex justify-between items-center p-2 bg-muted rounded-md">
              <span className="text-sm text-muted-foreground">面積:</span>
              <span className="font-medium">{measurements.area}</span>
            </div>
          )}
          
          {!measurements.distance && !measurements.area && (
            <div className="text-center py-4 text-muted-foreground text-sm">
              選擇測量工具開始測量
            </div>
          )}
        </div>

        {/* 操作按鈕 */}
        {(measurements.distance || measurements.area) && (
          <>
            <Separator />
            <Button
              variant="outline"
              size="sm"
              className="w-full"
              onClick={clearMeasurements}
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              清除測量
            </Button>
          </>
        )}

        {/* 使用說明 */}
        {isDrawing && (
          <div className="text-xs text-muted-foreground bg-muted/50 p-2 rounded-md">
            {measurementType === 'distance' 
              ? '點擊地圖添加測量點，雙擊結束測量'
              : '點擊地圖添加多邊形頂點，雙擊結束測量'
            }
          </div>
        )}
      </CardContent>
    </Card>
  );
}
